{"title": "Notification Settings", "send_summary": "Send Sales Summary", "send_summary_d": "Set up notifications for your sales summary", "send_changed_notif": "Send Notification for Changes", "send_changed_notif_d": "Set up notifications if there any changes to your data", "exp_notif": "Expired Stock Notification", "exp_notif_d": "Set notification for expired stock", "set_notif": "Set Notifications", "back": "Back", "save": "Save", "activate_notif": "Activate Notification: Added Data", "activate_notif_tooltip": "Notification will appear on dashboard and POS", "activate_notif_d": "majoo will send notifications when admin add the following data:", "produk": "Product", "promo": "Promo", "harga_ojek_online": "Ojek Online Price", "group_harga_spesial": "Special Price Group", "changed_notif": "Activate Notification: Changed Data", "changed_notif_d": "majo<PERSON> will send notifications when admin change the following data:", "receiver": "Notification Recipient", "receiver_desc": "Notifications will be sent to the selected employee's Majoo app", "receiver_2": "Employee/Receiver", "choose_emp": "Select Employees", "emp_name": "Employee Name", "access": "Access Rights", "save_desc": "<strong>{{outlet_name}}</strong> outlet notifications will be saved according to the settings that have been made. Continue?", "continue": "Yes, Continue", "cancel": "Cancel", "reservation_notif": "Reservation Notification", "reservation_notif_d": "Set up notifications for your reservation", "toast": {"employee_f": "Failed to retrieve employee data", "access_f": "Failed to fetch access rights data", "success": "Success", "error_save": "Failed to save data", "save_desc": "<strong>{{outlet_name}}</strong> Notification Settings saved successfully"}, "whatsapp_notif_log": "WhatsApp Notification Log", "whatsapp_integration": "Send Reports via Integrated WhatsApp", "whatsapp_integration_desc": "Automate sending cashier closing reports, hourly sales reports, and various other reports through WhatsApp notification integration", "buy_addon": "Buy Add-On", "cash_register_report": "Cashier Closing Report", "cash_register_report_desc": "Automate sending cashier closing reports via WhatsApp", "hourly_sales_report": "Hourly Sales Report", "hourly_sales_report_desc": "Automate sending hourly sales reports via WhatsApp", "attendance_report": "Attendance Report", "attendance_report_desc": "Automate sending attendance reports via WhatsApp"}