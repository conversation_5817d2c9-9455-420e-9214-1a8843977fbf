import UnavailableFeature from '../pages/UnavailableFeature';
import asyncRoute from '../components/asyncRoute';

const mainRoutes = {
    path: '/',
    breadcrumbIgnore: true,
    childRoutes: [
        {
            path: 'feature-unavailable',
            breadcrumbIgnore: true,
            component: UnavailableFeature,
        },
        {
            path: 'dashboard',
            name: 'Dashboard Penjualan',
            pageTitle: 'Dashboard',
            breadcrumbIgnore: true,
            component: asyncRoute(() => import('../pages/Dashboard/Dashboard')),
        },
        {
            path: 'shopee-callback/:outletId',
            name: 'Shopee Callback',
            pageTitle: 'Shopee Callback',
            breadcrumbIgnore: true,
            component: asyncRoute(() => import('../pages/Shopee/ShopeeCallback')),
        },
        {
            path: 'sales-dashboard',
            name: 'Dashboard Penjualan',
            pageTitle: 'Dashboard',
            breadcrumbIgnore: true,
            component: asyncRoute(() => import('../pages/SalesDashboard')),
            // component: asyncRoute(() => import('../pages/Dashboard/DashboardDatamart')), // OLD PAGE DATAMART
        },
        {
            path: 'laporan',
            name: '<PERSON><PERSON>an',
            childRoutes: [
                {
                    component: asyncRoute(() => import('../pages/Report/Sales/Ringkasan')),
                    breadcrumbIgnore: true,
                    exact: true,
                },
                {
                    path: 'analisa-laporan',
                    name: 'Analisa Laporan',
                    childRoutes: [
                        {
                            path: 'waktu-transaksi',
                            name: 'Waktu Teramai Penjualan',
                            component: asyncRoute(() => import('../pages/Report/Analysis/TransactionTime')),
                        },
                        {
                            path: 'waktu-produk',
                            name: 'Waktu Teramai Produk',
                            component: asyncRoute(() => import('../pages/Report/Analysis/ProductTime')),
                        },
                        {
                            path: 'kepuasan-pelanggan',
                            name: 'Kepuasan Pelanggan',
                            component: asyncRoute(() => import('../pages/Report/Analysis/CustomerSatisfaction')),
                        },
                        {
                            path: 'sales-waktu-transaksi',
                            name: 'Waktu Teramai',
                            component: asyncRoute(() => import('../pages/Report/Analysis/Datamart/TransactionTimeDatamartV2')),
                        },
                        {
                            path: 'sales-waktu-produk',
                            name: 'Waktu Teramai Produk',
                            component: asyncRoute(() => import('../pages/Report/Analysis/DatamartProductTime')),
                        },
                        {
                            path: 'perputaran-stok',
                            name: 'Perputaran Stok',
                            component: asyncRoute(() => import('../pages/Report/Analysis/StockAnalyst')),
                            service: 'non-datamart',
                        },
                        {
                            path: 'sales-perputaran-stok',
                            name: 'Perputaran Stok',
                            component: asyncRoute(() => import('../pages/Report/Analysis/StockAnalyst')), // TODO: need to check untuk source page-nya sama antara path datamart & non datamart
                            service: 'datamart',
                        },
                    ],
                },
                {
                    path: 'penjualan',
                    name: 'Laporan Penjualan',
                    childRoutes: [
                        {
                            component: asyncRoute(() => import('../pages/Report/Sales/Ringkasan')),
                            breadcrumbIgnore: true,
                            exact: true,
                        },
                        {
                            path: 'penjualan-cabang',
                            name: 'Penjualan Outlet',
                            component: asyncRoute(() => import('../pages/Report/Sales/Branch')),
                        },
                        {
                            path: 'sales-outlet',
                            name: 'Penjualan Outlet',
                            component: asyncRoute(() => import('../pages/Report/Sales/BranchDatamart')),
                        },
                        {
                            path: 'summary',
                            name: 'Ringkasan Penjualan',
                            component: asyncRoute(() => import('../pages/Report/Sales/Ringkasan')),
                        },
                        {
                            path: 'sales-summary',
                            name: 'Ringkasan Penjualan',
                            component: asyncRoute(() => import('../pages/Report/Sales/Ringkasan/Datamart')),
                        },
                        {
                            path: 'per-terminal',
                            name: 'Penjualan Per Terminal',
                            component: asyncRoute(() => import('../pages/Report/Sales/DeviceReportV3')),
                        },
                        {
                            path: 'per-kasir',
                            name: 'Laporan Per Kasir',
                            component: asyncRoute(() => import('../pages/Report/PerKasir/index')),
                        },
                        {
                            path: 'sales-per-kasir',
                            name: 'Laporan Per Kasir',
                            component: asyncRoute(() => import('../pages/Report/PerKasir/datamart/index')),
                        },
                        {
                            path: 'product-sales-per-kasir',
                            name: 'Penjualan Produk Per Kasir',
                            component: asyncRoute(() => import('../pages/Report/ProdukPerKasir')),
                        },
                        {
                            path: 'sales-kategori',
                            name: 'Penjualan Kategori',
                            component: asyncRoute(() => import('../pages/Report/Sales/Category/CategoryDatamart')),
                        },
                        {
                            path: 'sales-department',
                            name: 'Penjualan Departemen',
                            component: asyncRoute(() => import('../pages/Report/Sales/DepartemenDatamart/index.v2')),
                        },
                        {
                            path: 'sales-item',
                            name: 'Penjualan Produk',
                            component: asyncRoute(() => import('../pages/Report/Sales/ItemV2/Datamart')),
                        },
                        {
                            path: 'sales-varian',
                            name: 'Penjualan Ekstra',
                            childRoutes: [
                                {
                                    component: asyncRoute(() => import('../pages/Report/Sales/VarianV2/Datamart')),
                                    breadcrumbIgnore: true,
                                    exact: true,
                                },
                                {
                                    path: 'detail/:id',
                                    name: 'Penjualan Ekstra Detail',
                                    component: asyncRoute(() => import('../pages/Report/Sales/VarianV2/Datamart/detail')),
                                },
                            ],
                        },
                        {
                            path: 'sales-sub-varian',
                            name: 'Penjualan Sub Ekstra',
                            component: asyncRoute(() => import('../pages/Report/Sales/SubVarian/SubVarianDatamart')),
                        },
                        {
                            path: 'deposit-kedaluwarsa',
                            name: 'Deposit Kedaluwarsa',
                            component: asyncRoute(() => import('../pages/Report/Sales/Deposit/Retina/DepositKedaluwarsaV2')),
                        },
                        {
                            path: 'deposit',
                            name: 'Penjualan Deposit',
                            component: asyncRoute(() => import('../pages/Report/Sales/Deposit/PenjualanDeposit')),
                        },
                        {
                            path: 'sisa-deposit',
                            name: 'Sisa Deposit',
                            childRoutes: [
                                {
                                    component: asyncRoute(() => import('../pages/Report/Sales/Deposit/SisaDeposit')),
                                    breadcrumbIgnore: true,
                                    exact: true,
                                },
                                {
                                    path: 'riwayat/:id/:date',
                                    name: 'Riwayat Penggunaan Deposit',
                                    component: asyncRoute(() => import('../pages/Report/Sales/Deposit/SisaDepositDetail')),
                                },
                            ],
                        },
                        {
                            path: 'sales-harian',
                            name: 'Penjualan per Periode',
                            component: asyncRoute(() => import('../pages/Report/Sales/Harian/Datamart')),
                        },
                        {
                            path: 'promo',
                            name: 'Laporan Promo',
                            childRoutes: [
                                {
                                    component: asyncRoute(() => import('../pages/Report/Sales/SalesPromo')),
                                    breadcrumbIgnore: true,
                                    exact: true,
                                },
                                {
                                    path: 'detail/:key/:date/:id/:periode',
                                    name: 'Detail Laporan Promo',
                                    component: asyncRoute(() => import('../pages/Report/Sales/SalesPromo/detail')),
                                },
                            ],
                        },
                        {
                            path: 'sales-promo',
                            name: 'Laporan Promo',
                            childRoutes: [
                                {
                                    component: asyncRoute(() => import('../pages/Report/Sales/SalesPromoDatamart')),
                                    breadcrumbIgnore: true,
                                    exact: true,
                                },
                                {
                                    path: 'detail/:id',
                                    name: 'Detail Laporan Promo',
                                    component: asyncRoute(() => import('../pages/Report/Sales/SalesPromoDatamart/detail')),
                                },
                            ],
                        },
                        {
                            path: 'sales-jenis-pembayaran',
                            name: 'Laporan Jenis Bayar',
                            childRoutes: [
                                {
                                    component: asyncRoute(() => import('../pages/Report/Sales/PaymentMethodV2/Datamart')),
                                    breadcrumbIgnore: true,
                                    exact: true,
                                },
                                {
                                    path: 'detail/:id',
                                    name: 'Detail Jenis Pembayaran',
                                    component: asyncRoute(() => import('../pages/Report/Sales/PaymentMethodDetail/Datamart')),
                                },
                            ],
                        },
                        {
                            path: 'sales-jenis-order',
                            name: 'Laporan Jenis Order',
                            childRoutes: [
                                {
                                    component: asyncRoute(() => import('../pages/Report/Sales/OrderTypeDatamart')),
                                    breadcrumbIgnore: true,
                                    exact: true,
                                },
                                {
                                    path: 'detail/:id',
                                    name: 'Detail',
                                    component: asyncRoute(() => import('../pages/Report/Sales/OrderTypeDatamart/Detail')),
                                },
                            ],
                        },
                        {
                            path: 'serial-number',
                            name: 'Laporan Serial Number',
                            component: asyncRoute(() => import('../pages/Report/Sales/SerialNumber/SerialNumber')),
                        },
                        {
                            path: 'batch-number',
                            name: 'Laporan Batch Number',
                            component: asyncRoute(() => import('../pages/Report/Sales/BatchNumber/BatchNumber')),
                        },
                        {
                            path: 'compliment',
                            name: 'Laporan Komplimen',
                            component: asyncRoute(() => import('../pages/Report/Sales/Compliment')),
                        },
                        {
                            path: 'sales-compliment',
                            name: 'Laporan Komplimen',
                            component: asyncRoute(() => import('../pages/Report/Sales/Compliment/Datamart')),
                        },
                        {
                            path: 'sales-refund',
                            name: 'Laporan Refund',
                            component: asyncRoute(() => import('../pages/Report/Sales/RefundSales/indexDatamart')),
                        },
                        {
                            path: 'reservasi',
                            name: 'Laporan Reservasi',
                            component: asyncRoute(() => import('../pages/Report/Sales/Reservasi/Retina/ReservasiV2')),
                        },
                        {
                            path: 'reservasi-new',
                            name: 'Laporan Reservasi',
                            childRoutes: [
                                {
                                    component: asyncRoute(() => import('../pages/Report/Sales/Reservation')),
                                    breadcrumbIgnore: true,
                                    exact: true,
                                },
                                {
                                    path: 'utilisasi',
                                    name: 'Detail Utilisasi',
                                    component: asyncRoute(() => import('../pages/Report/Sales/Reservation/views/UtilizationDetail')),
                                },
                            ],
                        },
                        {
                            path: 'jasa',
                            name: 'Laporan Jasa',
                            component: asyncRoute(() => import('../pages/Report/Sales/Jasa')),
                        },
                        {
                            path: 'pajak',
                            name: 'Laporan Pajak',
                            childRoutes: [
                                {
                                    component: asyncRoute(() => import('../pages/Report/Sales/Tax')),
                                    breadcrumbIgnore: true,
                                    exact: true,
                                },
                                {
                                    path: 'detail/:id',
                                    name: 'Detail Laporan Pajak',
                                    component: asyncRoute(() => import('../pages/Report/Sales/TaxDetail')),
                                },
                            ],
                        },
                        {
                            path: 'sales-pajak',
                            name: 'Laporan Pajak',
                            childRoutes: [
                                {
                                    component: asyncRoute(() => import('../pages/Report/Sales/Tax/Datamart')),
                                    breadcrumbIgnore: true,
                                    exact: true,
                                },
                                {
                                    path: 'detail/:id',
                                    name: 'Detail Laporan Pajak',
                                    component: asyncRoute(() => import('../pages/Report/Sales/TaxDetail/Datamart')),
                                },
                            ],
                        },
                        {
                            path: 'void',
                            name: 'Laporan Void',
                            component: asyncRoute(() => import('../pages/Report/Void/V3')),
                        },
                        {
                            path: 'sales-pelanggan',
                            name: 'Laporan Pelanggan',
                            component: asyncRoute(() => import('../pages/Report/Sales/CustomerDatamart')),
                        },
                        {
                            path: 'transaksi-v2',
                            name: 'Detail Penjualan',
                            exact: true,
                            component: asyncRoute(() => import('../pages/Report/TransactionV2')),
                        },
                        {
                            path: 'transaksi-v2/:id',
                            name: 'Detail Penjualan',
                            exact: true,
                            component: asyncRoute(() => import('../pages/Report/TransactionV2')),
                        },
                        {
                            path: 'tutup-kasir',
                            name: 'Laporan Tutup Kasir',
                            childRoutes: [
                                {
                                    component: asyncRoute(() => import('../pages/Report/Sales/TutupKasir')),
                                    exact: true,
                                },
                                {
                                    path: ':id',
                                    component: asyncRoute(() => import('../pages/Report/Sales/TutupKasir/index.detail')),
                                },
                            ],
                        },
                        {
                            path: 'sales-poin',
                            name: 'Laporan Poin',
                            childRoutes: [
                                {
                                    component: asyncRoute(() => import('../pages/Report/Sales/Poin/Datamart')),
                                    breadcrumbIgnore: true,
                                    exact: true,
                                },
                                {
                                    path: 'detail',
                                    name: 'Laporan Poin Detail',
                                    component: asyncRoute(() => import('../pages/Report/Sales/Poin/Datamart/detail')),
                                },
                            ],
                        },
                        {
                            path: 'sales-voucher',
                            name: 'Laporan Kupon',
                            childRoutes: [
                                {
                                    component: asyncRoute(() => import('../pages/Report/Sales/VoucherDatamart')),
                                    breadcrumbIgnore: true,
                                    exact: true,
                                },
                                {
                                    path: 'detail/:id',
                                    name: 'Detail Laporan Kupon',
                                    component: asyncRoute(() => import('../pages/Report/Sales/VoucherDatamart/SalesVoucherDetail')),
                                },
                            ],
                        },
                        {
                            path: 'kas-kecil',
                            name: 'Laporan Kas Kasir',
                            component: asyncRoute(() => import('../pages/Report/Sales/KasKecil/Retina/KasKecil')),
                        },
                    ],
                },
                {
                    path: 'dapur',
                    name: 'Laporan Dapur',
                    childRoutes: [
                        {
                            path: 'proses-order',
                            name: 'Laporan Waktu Proses Order',
                            component: asyncRoute(() => import('../pages/Report/KitchenDisplay/OrderTime/index')),
                        },
                        {
                            path: 'proses-produk',
                            name: 'Laporan Waktu Proses Produk',
                            component: asyncRoute(() => import('../pages/Report/KitchenDisplay/OrderProduct/index')),
                        },
                    ],
                },
                {
                    path: 'karyawan',
                    name: 'Laporan Karyawan',
                    childRoutes: [
                        {
                            path: 'absensi',
                            name: 'Absensi',
                            component: asyncRoute(() => import('../pages/Report/Employee/Attendance/V3')),
                        },
                        {
                            path: 'komisi',
                            name: 'Laporan Komisi',
                            childRoutes: [
                                {
                                    path: 'ringkasan',
                                    name: 'Ringkasan Laporan',
                                    childRoutes: [
                                        {
                                            component: asyncRoute(() => import('../pages/Report/Employee/Retina/CommissionV3')),
                                            breadcrumbIgnore: true,
                                            exact: true,
                                        },
                                        {
                                            path: 'per-transaksi/:name/:id/:outlet',
                                            name: 'Detail Komisi',
                                            component: asyncRoute(() => import('../pages/Report/Employee/Retina/DetailCommissionTrxV3')),
                                        },
                                        {
                                            path: 'per-jenis-komisi/:name/:id/:outlet',
                                            name: 'Ringkasan Komisi',
                                            component: asyncRoute(() => import('../pages/Report/Employee/Retina/RingkasanCommissionV3')),
                                        },
                                    ],
                                },
                            ],
                        },
                    ],
                },
                {
                    path: 'persediaan',
                    name: 'Laporan Persediaan',
                    childRoutes: [
                        {
                            path: 'ringkasan',
                            name: 'Laporan Ringkasan Persediaan',
                            component: asyncRoute(() => import('../pages/Report/Inventory/SummaryV2')),
                        },
                        {
                            path: 'stok-kedaluwarsa',
                            name: 'Laporan Stok Kedaluwarsa',
                            component: asyncRoute(() => import('../pages/Report/Inventory/StockExpired')),
                        },
                        {
                            path: 'detail-persediaan',
                            name: 'Laporan Detail Persediaan',
                            component: asyncRoute(() => import('../pages/Report/Inventory/DetailPersediaan')),
                        },
                        {
                            path: 'ringkasan-persediaan',
                            name: 'Laporan Ringkasan Persediaan',
                            component: asyncRoute(() => import('../pages/Report/Inventory/RingkasanPersediaan')),
                        },
                    ],
                },
                // TODO: hapus menu dan folder ini jika settlement-v2 sudah aman
                {
                    path: 'settlement',
                    name: 'Laporan Settlement',
                    childRoutes: [
                        {
                            name: 'Settlement QRIS',
                            component: asyncRoute(() => import('../pages/Report/Sales/SettlementV1/Settlement')),
                            exact: true,
                        },
                        {
                            name: 'Detail Settlement',
                            path: 'detail/:id',
                            component: asyncRoute(() => import('../pages/Report/Sales/SettlementV1/Settlement')),
                        },
                    ],
                },
                {
                    path: 'settlement-v2', // TODO: update ke settlement jika sudah aman
                    name: 'Laporan Settlement',
                    childRoutes: [
                        {
                            name: 'Settlement QRIS',
                            component: asyncRoute(() => import('../pages/Report/Sales/Settlement/Settlement')),
                            exact: true,
                        },
                        {
                            name: 'Detail Settlement',
                            path: 'detail/:id',
                            component: asyncRoute(() => import('../pages/Report/Sales/Settlement/Settlement')),
                        },
                    ],
                },
                {
                    path: 'webstore/saldo',
                    name: 'Laporan Settlement',
                    childRoutes: [
                        {
                            name: 'Web Store',
                            component: asyncRoute(() => import('../pages/webOrder/WebStore/Saldo/V3')),
                            exact: true,
                        },
                        {
                            name: 'Web Store',
                            path: 'tarik',
                            component: asyncRoute(() => import('../pages/webOrder/WebStore/Saldo/V3/BalanceWithdrawal')),
                        },
                    ],
                },
            ],
        },
        {
            path: 'item',
            name: 'Produk',
            childRoutes: [
                {
                    name: 'Daftar Produk',
                    component: asyncRoute(() => import('../pages/ItemV2')),
                    exact: true,
                },
                {
                    path: 'custom-menu-book',
                    name: 'Buku Menu',
                    component: asyncRoute(() => import('../pages/Item/CustomMenuBook')),
                },
                {
                    path: 'service',
                    name: 'Daftar Layanan',
                    component: asyncRoute(() => import('../pages/Item/Service')),
                },
                {
                    path: 'category',
                    name: 'Daftar Kategori',
                    component: asyncRoute(() => import('../pages/Item/Category')),
                },
                {
                    path: 'addon',
                    name: 'Produk Ekstra',
                    component: asyncRoute(() => import('../pages/Item/AddOnV2')),
                },
                {
                    path: 'department',
                    name: 'Daftar Departemen',
                    component: asyncRoute(() => import('../pages/Item/Department')),
                },
                {
                    path: 'deposit',
                    name: 'Deposit',
                    component: asyncRoute(() => import('../pages/Item/Deposit')),
                },
                {
                    path: 'ojek-online-price',
                    name: 'Daftar Harga Ojek Online',
                    component: asyncRoute(() => import('../pages/Item/OjekOnlinePrice')),
                },
                {
                    path: 'scheduler-price',
                    name: 'Daftar Penjadwalan Harga',
                    component: asyncRoute(() => import('../pages/Item/SchedulerPrice')),
                },
                {
                    path: 'price-tag-print',
                    name: 'Cetak Barcode',
                    component: asyncRoute(() => import('../pages/Item/PriceTagPrint')),
                },
                {
                    path: 'bundles',
                    name: 'Produk Paket',
                    component: asyncRoute(() => import('../pages/ProductBundle')),
                },
                {
                    path: 'notes-category',
                    name: 'Daftar Kategori Catatan',
                    component: asyncRoute(() => import('../pages/Item/NotesCategory')),
                },
                {
                    path: 'recipe',
                    name: 'Master Resep',
                    component: asyncRoute(() => import('../pages/Item/RecipeMaster')),
                },
            ],
        },
        {
            path: 'inventory',
            name: 'Inventori',
            childRoutes: [
                {
                    path: 'kelola-stok',
                    name: 'Kelola Stok',
                    childRoutes: [
                        {
                            path: 'daftar-stok',
                            name: 'Daftar Stok',
                            component: asyncRoute(() => import('../pages/Inventory/StockList/index')),
                        },
                        {
                            path: 'stok-masuk',
                            name: 'Faktur Pembelian',
                            component: asyncRoute(() => import('../pages/Inventory/StockEntryV2')),
                        },
                        {
                            path: 'stok-produksi',
                            name: 'Produksi Stok',
                            childRoutes: [
                                {
                                    path: 'daftar-stok-produksi',
                                    name: 'Daftar Produksi Stok',
                                    component: asyncRoute(() => import('../pages/Inventory/StockProduksiV2')),
                                },
                                {
                                    path: 'template-stok-produksi',
                                    name: 'Template Produksi Stok',
                                    component: asyncRoute(() => import('../pages/Inventory/StockProduksiTemplateV2')),
                                },
                            ],
                        },
                        {
                            path: 'stock-opname',
                            name: 'Stok Opname',
                            childRoutes: [
                                {
                                    component: asyncRoute(() => import('../pages/Inventory/StockOpname/StockOpnameV2')),
                                    breadcrumbIgnore: true,
                                    exact: true,
                                },
                                {
                                    path: ':type/:id?',
                                    name: 'Detail',
                                    component: asyncRoute(() => import('../pages/Inventory/StockOpname/V2/StockOpnameCreate')),
                                    breadcrumbIgnore: true,
                                },
                            ],
                        },
                        {
                            path: 'stock-terbuang',
                            name: 'Stok Terbuang',
                            childRoutes: [
                                {
                                    component: asyncRoute(() => import('../pages/Inventory/StockTerbuang/StockTerbuang')),
                                    breadcrumbIgnore: true,
                                    exact: true,
                                },
                            ],
                        },
                    ],
                },
                {
                    path: 'pembelian-stok',
                    name: 'Pembelian Stok',
                    childRoutes: [
                        {
                            path: 'supplier',
                            name: 'Daftar Pemasok',
                            childRoutes: [
                                {
                                    component: asyncRoute(() => import('../pages/Inventory/SupplierList')),
                                    breadcrumbIgnore: true,
                                    exact: true,
                                },
                            ],
                        },
                        {
                            path: 'purchase-order',
                            name: 'Pemesanan Stok',
                            component: asyncRoute(() => import('../pages/Inventory/PurchaseOrder/index')),
                        },
                        {
                            path: 'purchase-shipment',
                            name: 'Pengiriman Pembelian',
                            component: asyncRoute(() => import('../pages/Inventory/PurchaseShipment')),
                        },
                        {
                            path: 'product-request',
                            name: 'Permintaan Barang',
                            component: asyncRoute(() => import('../pages/Inventory/ProductRequest')),
                        },
                    ],
                },
                {
                    path: 'mutasi-stok',
                    name: 'Mutasi Antar Outlet',
                    childRoutes: [
                        {
                            path: 'permintaan-stok',
                            name: 'Permintaan Stok',
                            component: asyncRoute(() => import('../pages/Inventory/RequestStock')),
                        },
                        {
                            path: 'kirim-transfer-stok',
                            name: 'Kirim Stok',
                            component: asyncRoute(() => import('../pages/Inventory/TransferStock/V2')),
                        },
                        {
                            path: 'terima-mutasi-stok',
                            name: 'Terima Mutasi Stok',
                            component: asyncRoute(() => import('../pages/Inventory/MutationStockEntry')),
                        },
                        {
                            path: 'terima-transfer-stok',
                            name: 'Stok Transit',
                            component: asyncRoute(() => import('../pages/Inventory/StockTransitV2')),
                        },
                    ],
                },
                {
                    path: 'retur',
                    name: 'Retur',
                    childRoutes: [
                        {
                            path: 'retur-pembelian',
                            name: 'Retur Pembelian',
                            component: asyncRoute(() => import('../pages/Inventory/PurchaseReturn')),
                        },
                        {
                            path: 'rekonsiliasi-retur',
                            name: 'Rekonsiliasi Retur',
                            component: asyncRoute(() => import('../pages/Inventory/ReturnReconciliation')),
                        },
                    ],
                },
            ],
        },
        {
            path: 'pelanggan',
            name: 'Pelanggan',
            childRoutes: [
                {
                    name: 'Daftar Pelanggan',
                    component: asyncRoute(() => import('../pages/Customer/CustomerList')),
                    breadcrumbIgnore: true,
                    exact: true,
                },
                {
                    path: 'daftar-pelanggan',
                    name: 'Daftar Pelanggan',
                    childRoutes: [
                        {
                            component: asyncRoute(() => import('../pages/Customer/CustomerList')),
                            breadcrumbIgnore: true,
                            exact: true,
                        },
                        {
                            path: ':type/:id',
                            name: 'Detail',
                            component: asyncRoute(() => import('../pages/Customer/CustomerDetail')),
                        },
                    ],
                },
                {
                    path: 'grup-pelanggan',
                    name: 'Grup Pelanggan',
                    component: asyncRoute(() => import('../pages/Customer/CustomerGroup')),
                },
                {
                    path: 'special-price-group',
                    name: 'Grup Harga Spesial',
                    component: asyncRoute(() => import('../pages/Customer/PriceListV2/indexV2')),
                },
                {
                    path: 'kustom-data',
                    name: 'Kustom Data Pelanggan',
                    component: asyncRoute(() => import('../pages/Customer/CustomerDataCustom')),
                },
            ],
        },
        {
            path: 'promosi',
            name: 'Promosi',
            childRoutes: [
                {
                    path: 'voucher-diskon',
                    name: 'Kupon Diskon',
                    childRoutes: [
                        {
                            path: 'voucher-campaign',
                            name: 'Kupon Campaign',
                            component: asyncRoute(() => import('../pages/Promotion/VoucherCampaignV2')),
                        },
                        {
                            path: 'voucher',
                            name: 'Kupon List',
                            component: asyncRoute(() => import('../pages/Promotion/VoucherList')),
                        },
                    ],
                },
                {
                    path: 'promo',
                    name: 'Promo',
                    childRoutes: [
                        {
                            path: 'advance-redesign',
                            name: 'Advance',
                            component: asyncRoute(() => import('../pages/Promotion/Redesign/AdvancePromo')),
                        },
                        {
                            path: 'basic-redesign',
                            name: 'Basic',
                            component: asyncRoute(() => import('../pages/Promotion/Redesign/AdvancePromo')),
                        },
                        {
                            path: 'basic',
                            name: 'Basic',
                            component: asyncRoute(() => import('../pages/Promotion/PromoBasicV2')),
                        },
                        {
                            path: 'invoice',
                            name: 'Per Total Pembelian',
                            component: asyncRoute(() => import('../pages/Promotion/PromoPerPurchaseV2')),
                        },
                        {
                            path: 'product',
                            name: 'Per Produk',
                            component: asyncRoute(() => import('../pages/Promotion/PromoPerProduct')),
                        },
                    ],
                },
                {
                    path: 'poin',
                    name: 'Poin Reward',
                    childRoutes: [
                        {
                            path: 'invoice',
                            name: 'Per Total Pembelian',
                            component: asyncRoute(() => import('../pages/RewardPointV2/PerPurchase')),
                        },
                        {
                            path: 'product',
                            name: 'Per Produk',
                            component: asyncRoute(() => import('../pages/RewardPointV2/PerProduct')),
                        },
                        {
                            path: 'redeem-setting',
                            name: 'Penukaran Poin',
                            component: asyncRoute(() => import('../pages/Promotion/PoinSetting')),
                        },
                    ],
                },
            ],
        },
        {
            path: 'commission',
            name: 'Komisi',
            childRoutes: [
                {
                    path: 'group',
                    name: 'Daftar Grup Komisi',
                    component: asyncRoute(() => import('../pages/Commission/Commission')),
                },
            ],
        },
        {
            path: 'marketing',
            name: 'Marketing',
            childRoutes: [
                {
                    path: 'kampanye',
                    name: 'Daftar Kampanye Marketing',
                    component: asyncRoute(() => import('../pages/Marketing/CampaignV3')),
                },
                {
                    path: 'transaction',
                    name: 'Daftar Paket Kampanye Marketing',
                    component: asyncRoute(() => import('../pages/Marketing/TransactionV3')),
                },
            ],
        },
        {
            path: 'pengaturan-bisnis',
            name: 'Pengaturan Bisnis',
            childRoutes: [
                {
                    path: 'cabang',
                    name: 'Daftar Outlet',
                    childRoutes: [
                        {
                            component: asyncRoute(() => import('../pages/Settings/Branch/Branch')),
                            breadcrumbIgnore: true,
                            exact: true,
                        },
                        {
                            path: 'detail/:id',
                            component: asyncRoute(() => import('../pages/Settings/Branch/BranchDetail')),
                            name: 'Detail Outlet',
                        },
                    ],
                },
                {
                    path: 'grup-outlet',
                    name: 'Grup Outlet',
                    childRoutes: [
                        {
                            component: asyncRoute(() => import('../pages/Settings/BranchGroup')),
                            breadcrumbIgnore: true,
                            exact: true,
                        },
                    ],
                },
                {
                    path: 'notification/dashboard',
                    name: 'Pengaturan Notifikasi Dashboard',
                    component: asyncRoute(() => import('../pages/Settings/Account/NotificationDashboard')),
                },
                {
                    path: 'notification',
                    name: 'Notifikasi',
                    component: asyncRoute(() => import('../pages/Settings/Account/NotificationV3')),
                },
                {
                    path: 'support-access',
                    name: 'Akses Support',
                    component: asyncRoute(() => import('../pages/Settings/Support/SupportAccessV2')),
                },
                {
                    path: 'change-notification',
                    name: 'Kirim Notifikasi Perubahan',
                    component: asyncRoute(() => import('../pages/Settings/Account/ChangeNotification')),
                },
                {
                    path: 'expired-notification',
                    name: 'Kirim Notifikasi Stok Kadaluwarsa',
                    component: asyncRoute(() => import('../pages/Settings/Account/ExpiredNotification')),
                },
                {
                    path: 'reservation-notification',
                    name: 'Notifikasi Reservasi',
                    component: asyncRoute(() => import('../pages/Settings/Account/ReservationNotification')),
                },
                {
                    path: 'tutup-kasir-notification',
                    name: 'Notifikasi Laporan Tutup Kasir',
                },
                {
                    path: 'floorplan-and-table',
                    name: 'Denah dan Meja',
                    component: asyncRoute(() => import('../pages/Settings/FloorPlan')),
                },
            ],
        },
        {
            path: 'payment-setting',
            name: 'Pengaturan Pembayaran',
            childRoutes: [
                {
                    path: 'receipt-and-charge',
                    name: 'Struk dan Biaya',
                    component: asyncRoute(() => import('../pages/Settings/Account/BillingInfo')),
                },
                {
                    path: 'receipt',
                    name: 'Struk',
                    component: asyncRoute(() => import('../pages/Settings/Account/Receipt')),
                },
                {
                    path: 'tax-setting',
                    breadcrumbIgnore: true,
                    childRoutes: [
                        {
                            component: asyncRoute(() => import('../pages/Settings/Account/TaxSetting')),
                            name: 'Pajak',
                            exact: true,
                        },
                        {
                            path: ':id',
                            name: 'Pengaturan Pajak',
                            component: asyncRoute(() => import('../pages/Settings/Account/TaxSetting/FormTaxSetting')),
                        },
                    ],
                },
                {
                    path: 'non-cash',
                    name: 'Pembayaran Non-Tunai',
                    component: asyncRoute(() => import('../pages/Settings/Payment/NonCashRetina')),
                },
            ],
        },
        {
            path: 'refund-setting',
            name: 'Pengaturan Refund',
            component: asyncRoute(() => import('../pages/Settings/Refund')),
        },
        {
            path: 'stock-notif-setting',
            name: 'Pengaturan Product Dan Inventory',
            childRoutes: [
                {
                    name: 'Pengaturan Product Dan Inventory',
                    component: asyncRoute(() => import('../pages/Settings/ProductInventory')),
                    exact: true,
                },
                {
                    path: 'stock-notification',
                    name: 'Pengaturan Notifikasi Stok',
                    component: asyncRoute(() => import('../pages/Settings/ProductInventory/StockNotification')),
                },
                {
                    path: 'stock-limitation',
                    name: 'Pengaturan Limitasi Stok Keluar',
                    component: asyncRoute(() => import('../pages/Settings/ProductInventory/StockLimitation')),
                },
                {
                    path: 'inventory-request',
                    name: 'Pengaturan Permintaan Stok',
                    component: asyncRoute(() => import('../pages/Settings/ProductInventory/InventoryRequest')),
                },
                {
                    path: 'purchase-price-settings',
                    name: 'Pengaturan Harga Beli',
                    component: asyncRoute(() => import('../pages/Settings/ProductInventory/PurchasePriceSettings')),
                },
                {
                    path: 'sales-extra-settings',
                    name: 'Pengaturan Penjualan Ekstra',
                    component: asyncRoute(() => import('../pages/Settings/ProductInventory/SalesExtraSettings')),
                },
                {
                    path: 'recipe-master-settings',
                    name: 'Pengaturan Resep Produk',
                    component: asyncRoute(() => import('../pages/Settings/ProductInventory/RecipeMasterSettings')),
                },
            ],
        },
        {
            path: 'reservation-setting',
            name: 'Pengaturan Reservasi',
            childRoutes: [
                {
                    name: 'Pengaturan Reservasi',
                    component: asyncRoute(() => import('../pages/Settings/Reservation')),
                    exact: true,
                },
                {
                    path: 'provider-schedule-settings',
                    name: 'Pengaturan Jadwal Penyedia Jasa',
                    component: asyncRoute(() => import('../pages/Settings/Reservation/ProviderScheduleSettings')),
                }
            ],
        },
        {
            path: 'satuan-barang',
            name: 'Satuan Barang',
            component: asyncRoute(() => import('../pages/Settings/Account/Satuan')),
        },
        {
            path: 'employee',
            name: 'Akses Karyawan',
            childRoutes: [
                {
                    name: 'Akses Karyawan',
                    component: asyncRoute(() => import('../pages/Settings/EmployeeAccess')),
                    exact: true,
                },
                {
                    path: 'dashboard',
                    name: 'Dashboard Karyawan',
                    component: asyncRoute(() => import('../pages/Payroll/EmployeeDashboard')),
                },
                {
                    path: 'employee-list',
                    name: 'Akses Karyawan',
                    component: asyncRoute(() => import('../pages/Settings/EmployeeAccess')),
                },
                {
                    path: 'attendance-access',
                    name: 'Akses Absensi',
                    component: asyncRoute(() => import('../pages/Settings/EmployeeAccess/AttendanceAccess')),
                },
                {
                    path: 'attendance-radius',
                    name: 'Radius Absensi',
                    component: asyncRoute(() => import('../pages/Settings/EmployeeAccess/AttendanceRadius')),
                },
                {
                    path: 'privilege-list',
                    name: 'Daftar Hak Akses',
                    component: asyncRoute(() => import('../pages/Settings/Employee/Privileges')),
                },
                {
                    path: 'employee-shift',
                    name: 'Jadwal Kerja Karyawan',
                    component: asyncRoute(() => import('../pages/Settings/Employee/Shift/Retina/index')),
                },
            ],
        },
        {
            path: 'employee-settings',
            name: 'Pengaturan Karyawan',
            childRoutes: [
                {
                    name: 'Daftar Karyawan',
                    component: asyncRoute(() => import('../pages/Settings/Employee/Employee')),
                    exact: true,
                },
                {
                    path: 'employee-list',
                    name: 'Daftar Karyawan',
                    component: asyncRoute(() => import('../pages/Settings/Employee/Employee')),
                },
            ],
        },
        {
            path: 'kasbon',
            name: 'Kasbon',
            childRoutes: [
                {
                    path: 'daftar-pengajuan-kasbon',
                    name: 'Daftar Pengajuan Kasbon',
                    childRoutes: [
                        {
                            component: asyncRoute(() => import('../pages/Settings/Employee/Kasbon/RequestList')),
                            exact: true,
                        },
                        {
                            path: 'history',
                            name: 'Riwayat Pengajuan Kasbon',
                            component: asyncRoute(() => import('../pages/Settings/Employee/Kasbon/RequestList/History')),
                        },
                    ],
                },
                {
                    path: 'kasbon-access-list',
                    name: 'Daftar Akses Kasbon',
                    component: asyncRoute(() => import('../pages/Settings/Employee/Kasbon/AccessList')),
                },
            ],
        },
        {
            path: 'payroll-menu',
            name: 'Payroll',
            childRoutes: [
                {
                    path: 'setting',
                    name: 'Pengaturan Payroll',
                    component: asyncRoute(() => import('../pages/Payroll/PayrollSetting')),
                },
            ],
        },
        {
            path: 'karyawan',
            name: 'Karyawan',
            childRoutes: [
                {
                    path: 'hak-akses',
                    name: 'Kustom Akses',
                    component: asyncRoute(() => import('../pages/Settings/Employee/PrivilegesRetina')),
                },
            ],
        },
        {
            path: 'print-setting',
            name: 'Cetak',
            childRoutes: [
                {
                    path: 'pdf',
                    name: 'PDF',
                    childRoutes: [
                        {
                            name: 'PDF',
                            component: asyncRoute(() => import('../pages/Settings/PrintSetting/Pdf')),
                            exact: true,
                        },
                        {
                            path: 'reservation',
                            name: 'PDF Reservasi',
                            component: asyncRoute(() => import('../pages/Settings/PrintSetting/Pdf/Reservation')),
                        },
                    ],
                },
            ],
        },
        {
            path: 'terminal-setting',
            name: 'Terminal',
            childRoutes: [
                {
                    path: 'device',
                    name: 'Daftar Perangkat',
                    component: asyncRoute(() => import('../pages/Settings/Employee/Device/Device')),
                },
                {
                    path: 'soundbox',
                    name: 'Soundbox Device',
                    component: asyncRoute(() => import('../pages/Settings/Soundbox')),
                },
            ],
        },
        {
            path: 'import-export-list',
            name: 'Daftar Impor & Ekspor',
            component: asyncRoute(() => import('../pages/Settings/ExportImportList')),
        },
        {
            path: 'user-profile',
            name: 'Akun Profil',
            childRoutes: [
                {
                    path: 'account',
                    name: 'Informasi Akun',
                    component: asyncRoute(() => import('../pages/Settings/UserProfile/AccountInfo')),
                },
                {
                    path: 'business',
                    name: 'Informasi Bisnis',
                    component: asyncRoute(() => import('../pages/Settings/UserProfile/BusinessInfoV3')),
                },
                {
                    path: 'bank-account',
                    name: 'Rekening Bank',
                    component: asyncRoute(() => import('../pages/Settings/UserProfile/BankAccount')),
                },
            ],
        },
        {
            path: 'message-setting',
            name: 'Notifikasi',
            childRoutes: [
                {
                    path: 'inbox',
                    name: 'Notifikasi',
                    childRoutes: [
                        {
                            name: 'Daftar Notifikasi',
                            component: asyncRoute(() => import('../pages/Settings/InboxV3')),
                            exact: true,
                        },
                        {
                            path: ':type/:id',
                            name: 'Detail Notifikasi',
                            component: asyncRoute(() => import('../pages/Settings/InboxDetail')),
                        },
                    ],
                },
                {
                    path: 'deleted-transaction',
                    name: 'Transaksi Dihapus',
                    component: asyncRoute(() => import('../pages/Settings/Notification/DeletedTransaction/v3')),
                },
                {
                    path: 'setting-whatsapp',
                    name: 'Pengaturan WhatsApp',
                    component: asyncRoute(() => import('../pages/Settings/Notification/WhatsApp')),
                },
            ],
        },
        {
            path: 'majoo-teams',
            name: 'majoo Teams',
            childRoutes: [
                {
                    path: 'access',
                    name: 'Akses majoo Teams',
                    component: asyncRoute(() => import('../pages/Settings/MajooTeams/AccessMajooTeams')),
                },
                {
                    path: 'send-notification',
                    name: 'Kirim Notifikasi',
                    component: asyncRoute(() => import('../pages/Settings/MajooTeams/SendNotification')),
                },
            ],
        },
        {
            path: 'gaji',
            name: 'Gaji',
            childRoutes: [
                {
                    path: 'laporan-pembayaran',
                    name: 'Laporan Pembayaran',
                    component: asyncRoute(() => import('../pages/Settings/Payroll/PaymentReport')),
                },
                {
                    path: 'struktur-gaji',
                    name: 'Struktur Gaji',
                    childRoutes: [
                        {
                            component: asyncRoute(() => import('../pages/Settings/Payroll/EmployeePayroll')),
                            breadcrumbIgnore: true,
                            exact: true,
                        },
                        {
                            path: ':type/:id',
                            name: 'Detail',
                            component: asyncRoute(() => import('../pages/Settings/Payroll/EmployeePayroll/detail')),
                        },
                    ],
                },
                {
                    path: 'pembayaran-payroll',
                    name: 'Pembayaran Payroll',
                    childRoutes: [
                        {
                            component: asyncRoute(() => import('../pages/Settings/Payroll/PaymentTransactions')),
                            breadcrumbIgnore: true,
                            exact: true,
                        },
                    ],
                },
                {
                    path: 'laporan-pembayaran',
                    name: 'Laporan Pembayaran',
                    component: asyncRoute(() => import('../pages/Settings/Payroll/PaymentReport')),
                },
                {
                    path: 'rekonsiliasi-pembayaran',
                    name: 'Rekonsiliasi Pembayaran',
                    component: asyncRoute(() => import('../pages/Settings/Payroll/PaymentReconciliation')),
                },
                {
                    path: 'list-mapping-payroll-account',
                    name: 'Daftar Pemetaan Akun Gaji',
                    childRoutes: [
                        {
                            component: asyncRoute(() => import('../pages/Settings/Payroll/MappingPayrollAccount')),
                            breadcrumbIgnore: true,
                            exact: true,
                        },
                        {
                            path: ':type/:id',
                            name: 'Detail',
                            component: asyncRoute(() => import('../pages/Settings/Payroll/MappingPayrollAccount/detail')),
                        },
                    ],
                },
            ],
        },
        {
            path: 'message',
            name: 'Pesan Masuk',
            childRoutes: [
                {
                    name: 'Daftar Pesan',
                    component: asyncRoute(() => import('../pages/Inbox/V3')),
                    exact: true,
                },
                {
                    path: 'inbox',
                    name: 'Daftar Pesan',
                    component: asyncRoute(() => import('../pages/Inbox/V3')),
                },
            ],
        },
        {
            path: 'kasir',
            name: 'Kasir',
            component: asyncRoute(() => import('../pages/Settings/Kasir')),
        },
        {
            path: 'kategori-kasir',
            name: 'Kategori Kasir',
            component: asyncRoute(() => import('../pages/Settings/CashierCategory')),
        },
        {
            path: '',
            name: 'Cabang',
        },
        {
            path: 'buku-kas',
            name: 'Buku Kas',
            childRoutes: [
                {
                    path: 'daftar-buku-kas',
                    name: 'Daftar Buku Kas & Bank',
                    component: asyncRoute(() => import('../pages/CashBook/CashBookList')),
                },
                {
                    path: 'detail/:id/:outletId?',
                    name: 'Detail Kas Bank',
                    component: asyncRoute(() => import('../pages/CashBook/CashBookDetail')),
                },
                {
                    path: 'daftar-transfer',
                    name: 'Daftar Transfer',
                    component: asyncRoute(() => import('../pages/CashBook/TransferList')),
                },
            ],
        },
        {
            path: 'laporan-keuangan',
            name: 'Laporan Keuangan',
            childRoutes: [
                {
                    name: 'Laporan Neraca',
                    component: asyncRoute(() => import('../pages/ReportFinance/NeracaV2')),
                    exact: true,
                },
                {
                    path: 'rugi-laba',
                    name: 'Laporan Laba Rugi',
                    component: asyncRoute(() => import('../pages/ReportFinance/ProfitLossReportV2')),
                },
                {
                    path: 'accounting-rugi-laba',
                    name: 'Laporan Laba Rugi',
                    component: asyncRoute(() => import('../pages/ReportFinance/ProfitLossReportDatamart')),
                },
                {
                    path: 'buku-besar',
                    name: 'Laporan Buku Besar',
                    component: asyncRoute(() => import('../pages/BukuBesar/BukuBesarListV2')),
                },
                {
                    path: 'accounting-buku-besar',
                    name: 'Laporan Buku Besar',
                    component: asyncRoute(() => import('../pages/BukuBesarDatamart')),
                },
                {
                    path: 'jurnal',
                    name: 'Laporan Jurnal',
                    component: asyncRoute(() => import('../pages/ReportFinance/JournalReportV2/JournalReportV2')),
                },
                {
                    path: 'accounting-jurnal',
                    name: 'Laporan Jurnal',
                    component: asyncRoute(() => import('../pages/ReportFinance/JournalReportDatamart')),
                },
                {
                    path: 'Neraca',
                    name: 'Laporan Neraca',
                    component: asyncRoute(() => import('../pages/ReportFinance/NeracaV2')),
                },
                {
                    path: 'accounting-neraca',
                    name: 'Laporan Neraca',
                    component: asyncRoute(() => import('../pages/ReportFinance/NeracaDatamart')),
                },
                {
                    path: 'arus-kas',
                    name: 'Laporan Arus Kas',
                    component: asyncRoute(() => import('../pages/ArusKasV2')),
                },
                {
                    path: 'accounting-arus-kas',
                    name: 'Laporan Arus Kas',
                    component: asyncRoute(() => import('../pages/ReportFinance/ArusKasDatamart')),
                },
                {
                    path: 'hutang',
                    name: 'Laporan Hutang',
                    component: asyncRoute(() => import('../pages/ReportFinance/DebtReport/Retina/DebtReport')),
                },
                {
                    path: 'accounting-hutang',
                    name: 'Laporan Hutang',
                    component: asyncRoute(() => import('../pages/ReportFinance/DebtReport/Datamart')),
                },
                {
                    path: 'piutang',
                    name: 'Laporan Piutang',
                    component: asyncRoute(() => import('../pages/LaporanPiutang/Retina/PiutangReport')),
                },
                {
                    path: 'accounting-piutang',
                    name: 'Laporan Piutang',
                    component: asyncRoute(() => import('../pages/LaporanPiutang/Datamart')),
                },
                {
                    path: 'konsultasi',
                    name: 'Laporan Keuangan - Konsultan',
                    component: asyncRoute(() => import('../pages/ReportFinance/FinanceReportConsultant')),
                },
            ],
        },
        {
            path: 'jurnal-umum-penyesuaian',
            name: 'Daftar Akun',
            childRoutes: [
                {
                    name: 'Jurnal Umum',
                    component: asyncRoute(() => import('../pages/JurnalUmum/V3')),
                    exact: true,
                },
                {
                    path: 'detail/:action/:id',
                    component: asyncRoute(() => import('../pages/JurnalUmum/V3/JurnalDetail')),
                    name: 'Edit Jurnal Umum',
                },
                {
                    path: 'detail/:action',
                    component: asyncRoute(() => import('../pages/JurnalUmum/V3/JurnalDetail')),
                    name: 'Tambah Jurnal Umum',
                },
            ],
        },
        {
            path: 'income',
            name: 'Pemasukan',
            childRoutes: [
                {
                    path: 'daftar-penerimaan',
                    name: 'Daftar Penerimaan',
                    component: asyncRoute(() => import('../pages/Income/IncomeListV2')),
                },
                {
                    path: 'reconciliation',
                    name: 'Rekonsiliasi Penerimaan Penjualan',
                    component: asyncRoute(() => import('../pages/Income/Settlement')),
                },
            ],
        },
        {
            path: 'pengeluaran',
            name: 'Pengeluaran',
            childRoutes: [
                {
                    path: 'daftar-pengeluaran',
                    name: 'Daftar Pengeluaran',
                    component: asyncRoute(() => import('../pages/Spending/SpendingList')),
                },
                {
                    path: 'daftar-biaya',
                    name: 'Daftar Biaya',
                    component: asyncRoute(() => import('../pages/DaftarBiaya')),
                },
                {
                    path: 'daftar-pembayaran-pembelian',
                    name: 'Daftar Pembayaran Pembelian',
                    component: asyncRoute(() => import('../pages/PurchasePayment/ListPurchasePayment')),
                },
                {
                    path: 'daftar-tagihan-rutin',
                    name: 'Daftar Tagihan Rutin',
                    component: asyncRoute(() => import('../pages/Spending/TagihanRutin')),
                },
                {
                    path: 'mitra',
                    name: 'Daftar Mitra',
                    childRoutes: [
                        {
                            component: asyncRoute(() => import('../pages/Spending/Mitra')),
                            breadcrumbIgnore: true,
                            exact: true,
                        },
                        {
                            path: ':type/:id?',
                            name: 'Detail',
                            component: asyncRoute(() => import('../pages/Spending/MitraDetail')),
                        },
                    ],
                },
            ],
        },
        {
            path: 'shopping',
            name: 'Supplies',
            childRoutes: [
                {
                    to: 'order',
                    exact: true,
                },
                {
                    path: 'transaction',
                    name: 'Daftar Belanja',
                    childRoutes: [
                        {
                            component: asyncRoute(() => import('../pages/Supplies/OrderHistory/indexV2')),
                            breadcrumbIgnore: true,
                            exact: true,
                        },
                        {
                            path: 'cek-status-pembayaran/:no',
                            component: asyncRoute(() => import('../pages/Supplies/CheckPaymentStatus')),
                            breadcrumbIgnore: true,
                        },
                        {
                            path: 'payment-status-success/:no',
                            component: asyncRoute(() => import('../pages/Supplies/PaymentStatus')),
                            breadcrumbIgnore: true,
                            isSuccesPayment: true,
                        },
                        {
                            path: 'payment-status-failed/:no',
                            component: asyncRoute(() => import('../pages/Supplies/PaymentStatus')),
                            breadcrumbIgnore: true,
                        },
                        {
                            path: 'order-check/:id',
                            component: asyncRoute(() => import('../pages/Supplies/CheckPaymentStatus')),
                            breadcrumbIgnore: true,
                        },
                    ],
                },
                {
                    path: 'order',
                    name: 'Belanja',
                    component: asyncRoute(() => import('../pages/Supplies/Layout/index')),
                    childRoutes: [
                        {
                            component: asyncRoute(() => import('../pages/Supplies/Landing/indexV2')),
                            breadcrumbIgnore: true,
                            exact: true,
                        },
                        {
                            path: 'category/all',
                            component: asyncRoute(() => import('../pages/Supplies/Category/indexV2')),
                            name: 'Kategori',
                        },
                        {
                            path: 'category/:id',
                            component: asyncRoute(() => import('../pages/Supplies/CategoryDetail/indexV2')),
                            name: 'Produk',
                            breadcrumbIgnore: true,
                        },
                        {
                            path: 'product/:merchantId/:outletId/:id',
                            component: asyncRoute(() => import('../pages/Supplies/ProductDetail/indexV2')),
                            name: 'Produk',
                            breadcrumbIgnore: true,
                        },
                        {
                            path: 'keranjang',
                            name: 'Keranjang',
                            childRoutes: [
                                {
                                    component: asyncRoute(() => import('../pages/Supplies/OrderDetailV2')),
                                    breadcrumbIgnore: true,
                                    exact: true,
                                },
                                {
                                    path: 'pembayaran',
                                    component: asyncRoute(() => import('../pages/Supplies/OrderCheckout/indexV2')),
                                    name: 'Pembayaran',
                                },
                            ],
                        },
                    ],
                },
            ],
        },
        {
            path: 'support',
            name: 'Langganan',
            childRoutes: [
                {
                    path: 'buy-old',
                    name: 'Langganan & Support',
                    component: asyncRoute(() => import('../pages/Support/buy/Support')),
                },
                {
                    path: 'buy',
                    name: 'Langganan & Support',
                    component: asyncRoute(() => import('../pages/Support/BuyV2')),
                },
                {
                    path: 'ticket',
                    name: 'Tiket Support',
                    component: asyncRoute(() => import('../pages/Support/Ticket/index')),
                },
                {
                    path: 'claim-voucher',
                    name: 'Klaim Voucher',
                    component: asyncRoute(() => import('../pages/Support/ClaimVoucher/index')),
                },
                {
                    path: 'feedback',
                    name: 'Masukan Perbaikan',
                    component: asyncRoute(() => import('../pages/Support/Feedback')),
                },
            ],
        },
        {
            path: 'guideline',
            name: 'Panduan Penggunaan',
            childRoutes: [
                {
                    to: 'online',
                    exact: true,
                },
                {
                    path: 'online',
                    name: 'Panduan Online',
                    childRoutes: [
                        {
                            component: asyncRoute(() => import('../pages/Help/Redesign/GeneralGuideline')),
                            breadcrumbIgnore: true,
                            exact: true,
                        },
                        {
                            path: 'article/:article_id',
                            name: 'Detail',
                            component: asyncRoute(() => import('../pages/Help/Redesign/DialogGuideline')),
                        },
                        {
                            path: 'search',
                            name: 'Search',
                            component: asyncRoute(() => import('../pages/Help/Redesign/SearchGuideline')),
                        },
                        {
                            path: 'faq',
                            name: 'FAQ',
                            component: asyncRoute(() => import('../pages/Help/Redesign/FaqGuideline')),
                        },
                    ],
                },
                {
                    path: 'video',
                    name: 'Panduan Video',
                    childRoutes: [
                        {
                            path: 'v2',
                            component: asyncRoute(() => import('../pages/Help/Redesign/VideoGuideline')),
                            name: 'Panduan Video Baru',
                        },
                    ],
                },
                {
                    path: 'update-majoo',
                    name: 'Update Majoo',
                    component: asyncRoute(() => import('../pages/Help/MajooJourney/MajooJourney')),
                },
            ],
        },
        {
            path: 'pembayaran-digital',
            name: 'Pembayaran Digital',
            childRoutes: [
                {
                    path: 'pengajuan-tsel-poin',
                    name: 'Pengajuan Telkomsel Poin',
                    childRoutes: [
                        {
                            component: asyncRoute(() => import('../pages/Support/DigitalPayment/TelkomselPoin/V3')),
                            breadcrumbIgnore: true,
                            exact: true,
                        },
                        {
                            path: ':type/:id?',
                            name: 'Detail',
                            component: asyncRoute(() => import('../pages/Support/DigitalPayment/TelkomselPoin/V3/TSelSubmissionDetail')),
                        },
                    ],
                },
                {
                    path: 'pengajuan-wallet',
                    name: 'Pengajuan Wallet',
                    childRoutes: [
                        {
                            component: asyncRoute(() => import('../pages/Support/DigitalPayment/QRISMajoo')),
                            breadcrumbIgnore: true,
                            exact: true,
                        },
                        {
                            path: ':type/:id?',
                            name: 'Detail',
                            component: asyncRoute(() => import('../pages/Support/DigitalPayment/WalletSubmission/WalletSubmissionDetail')),
                        },
                    ],
                },
                {
                    path: 'pengajuan-edc',
                    name: 'Pengajuan EDC',
                    childRoutes: [
                        {
                            component: asyncRoute(() => import('../pages/Support/DigitalPayment/EDCSubmission/index')),
                            breadcrumbIgnore: true,
                            exact: true,
                        },
                        {
                            path: ':type/:id?',
                            name: 'Detail',
                            component: asyncRoute(() => import('../pages/Support/DigitalPayment/EDCSubmission/components/form')),
                        },
                    ],
                },
            ],
        },
        {
            path: 'satu-sehat',
            name: 'Integrasi Satu Sehat',
            childRoutes: [
                {
                    path: 'setting',
                    name: 'Pengaturan Satu Sehat',
                    component: asyncRoute(() => import('../pages/Support/SatuSehatSetting')),

                },
                {
                    path: 'shipping-history',
                    name: 'Riwayat Pengiriman',
                    component: asyncRoute(() => import('../pages/Support/TransactionHistory')),

                },
                {
                    path: 'procedure-list',
                    name: 'Daftar Tindakan',
                    component: asyncRoute(() => import('../pages/Support/ProcedureList')),

                },
            ],
        },
        {
            path: 'consumer-app',
            name: 'Consumer App',
            childRoutes: [
                {
                    component: asyncRoute(() => import('../pages/ConsumerApp')),
                    breadcrumbIgnore: true,
                    exact: true,
                },
                {
                    path: 'activation',
                    name: 'Aktivasi Consumer App',
                    component: asyncRoute(() => import('../pages/ConsumerApp/views/ActivationForm')),
                },
                {
                    path: 'banner',
                    name: 'Banner Promosi',
                    component: asyncRoute(() => import('../pages/ConsumerApp/views/SetupBanner')),
                },
            ],
        },
        {
            path: 'toko-online',
            name: 'Toko Online',
            childRoutes: [
                {
                    path: 'pengaturan-penjualan',
                    name: 'Pengaturan Penjualan',
                    childRoutes: [
                        {
                            component: asyncRoute(() => import('../pages/TokoOnline/SalesSetting/index')),
                            exact: true,
                        },
                        {
                            path: 'collection',
                            name: 'Collection',
                            component: asyncRoute(() => import('../pages/TokoOnline/SalesSetting/Collection')),
                        },
                    ],
                },
                {
                    path: 'kustomisasi-toko',
                    name: 'Kustomisasi Toko',
                    component: asyncRoute(() => import('../pages/TokoOnline/ViewSetting')),
                },
                {
                    path: 'pengaturan-lain',
                    name: 'Pengaturan Lainnya',
                    component: asyncRoute(() => import('../pages/TokoOnline/OtherSetting')),
                },
            ],
        },
        {
            path: 'order-online',
            name: 'Order Online',
            childRoutes: [
                {
                    path: 'pengajuan-order-online',
                    name: 'Pengajuan Aktivasi',
                    childRoutes: [
                        {
                            // masih ada yang pakai di pages/Support/MarketPlace/Grab/PengajuanAwal/SubmissionGrab
                            component: asyncRoute(() => import('../pages/Support/MarketPlace/MarketPlace')),
                            breadcrumbIgnore: true,
                            exact: true,
                        },
                        {
                            // masih ada yang pakai di /pages/Support/MarketPlace/Grab/Merchant/GrabfoodMerchantSubmission
                            path: 'pengajuan-grab/:id',
                            name: 'Pengajuan Awal Grab',
                            component: asyncRoute(() => import('../pages/Support/MarketPlace/Grab/PengajuanAwal/SubmissionGrab')),
                            breadcrumbIgnore: true,
                        },
                    ],
                },
            ],
        },
        {
            path: 'grabfood',
            name: 'Grabfood',
            childRoutes: [
                {
                    component: asyncRoute(() => import('../pages/Grabfood/ListGrabItegration')),
                    breadcrumbIgnore: true,
                    exact: true,
                },
                {
                    path: 'integration/:id/:stepCount',
                    name: 'Pengajuan Integrasi Grab',
                    component: asyncRoute(() => import('../pages/Grabfood/GrabfoodIntegration')),
                },
                {
                    path: 'integration/:id',
                    name: 'Pengajuan Integrasi Grab',
                    component: asyncRoute(() => import('../pages/Grabfood/GrabfoodIntegration')),
                },
                {
                    path: 'pengaturan-grab-merchant/:id',
                    name: 'Pengaturan Grabfood',
                    childRoutes: [
                        {
                            component: asyncRoute(() => import('../pages/Grabfood/PengaturanGrabfood')),
                            breadcrumbIgnore: true,
                            exact: true,
                        },
                        {
                            path: 'promo',
                            name: 'Promo',
                            childRoutes: [
                                {
                                    component: asyncRoute(() => import('../pages/Support/MarketPlace/Grab/Promo/GrabPromo')),
                                    breadcrumbIgnore: true,
                                    exact: true,
                                },
                                {
                                    path: ':fromPage/:type/:idPromo',
                                    name: 'Promo Detail',
                                    component: asyncRoute(() => import('../pages/Support/MarketPlace/Grab/Promo/AddPromo')),
                                    breadcrumbIgnore: true,
                                    exact: true,
                                },
                                {
                                    path: ':fromPage/:type',
                                    name: 'Promo Detail',
                                    component: asyncRoute(() => import('../pages/Support/MarketPlace/Grab/Promo/AddPromo')),
                                    breadcrumbIgnore: true,
                                },
                            ],
                        },
                        {
                            path: 'product',
                            name: 'Pengaturan Produk Grab',
                            component: asyncRoute(() => import('../pages/Settings/Marketplace/PengaturanAwal')),
                            breadcrumbIgnore: true,
                        },
                    ],
                },
                {
                    path: 'pengaturan-awal-grab/:id/:stepCount',
                    name: 'Pengaturan Awal Grab',
                    component: asyncRoute(() => import('../pages/Support/MarketPlace/Grab/PengaturanAwal/PengaturanAwal')),
                },
                {
                    path: 'pengaturan-awal-grab/:id',
                    name: 'Pengaturan Awal Grab',
                    component: asyncRoute(() => import('../pages/Support/MarketPlace/Grab/PengaturanAwal/PengaturanAwal')),
                },
            ],
        },
        {
            path: 'tokopedia',
            name: 'Integrasi Tokopedia',
            childRoutes: [
                {
                    component: asyncRoute(() => import('../pages/Tokopedia/IntegrationList')),
                    breadcrumbIgnore: true,
                    exact: true,
                },
                {
                    path: 'integration-setting',
                    name: 'Pengaturan',
                    component: asyncRoute(() => import('../pages/Tokopedia/IntegrationSetting')),
                    breadcrumbIgnore: true,
                },
                {
                    path: 'product-setting',
                    name: 'Pengaturan Produk',
                    childRoutes: [
                        {
                            component: asyncRoute(() => import('../pages/Tokopedia/ProductSetting')),
                            breadcrumbIgnore: true,
                            exact: true,
                        },
                        {
                            path: ':type/:id',
                            name: 'Detail',
                            component: asyncRoute(() => import('../pages/Tokopedia/ProductDetail')),
                            exact: true,
                        },
                        {
                            path: ':type',
                            name: 'Detail',
                            component: asyncRoute(() => import('../pages/Tokopedia/ProductDetail')),
                        },
                    ],
                },
                {
                    path: 'initial-setup',
                    name: 'Pengaturan Awal',
                    component: asyncRoute(() => import('../pages/Tokopedia/InitialSetup')),
                },
            ],
        },
        {
            path: 'pengajuan-akun-digital',
            name: 'Pengajuan Akun Digital',
            childRoutes: [
                {
                    path: 'grab-merchant',
                    name: 'Akun Grabfood Merchant',
                    childRoutes: [
                        {
                            component: asyncRoute(() => import('../pages/Support/MarketPlace/Grab/Merchant/GrabfoodMerchantSubmission')),
                            breadcrumbIgnore: true,
                            exact: true,
                        },
                        {
                            path: ':type/:id?',
                            name: 'Detail',
                            component: asyncRoute(() => import('../pages/Support/MarketPlace/Grab/Merchant/GrabfoodMerchantSubmissionDetail')),
                            breadcrumbIgnore: true,
                        },
                    ],
                },
            ],
        },
        {
            path: 'blog',
            name: 'Inspirasi Wirausaha',
            childRoutes: [
                {
                    component: asyncRoute(() => import('../pages/Blog/Blog')),
                    breadcrumbIgnore: true,
                    exact: true,
                },
                {
                    path: 'inspirasi',
                    name: 'Inspirasi',
                    childRoutes: [
                        {
                            component: asyncRoute(() => import('../pages/Blog/Blog')),
                            breadcrumbIgnore: true,
                            exact: true,
                        },
                        {
                            path: 'detail/:slug',
                            name: 'Detail',
                            component: asyncRoute(() => import('../pages/Blog/BlogDetail')),
                        },
                        {
                            path: 'category/:id',
                            name: 'Kategori',
                            component: asyncRoute(() => import('../pages/Blog/BlogCategory')),
                        },
                    ],
                },
            ],
        },
        {
            path: 'majoo-preneur',
            name: 'majoo Preneur',
            childRoutes: [
                {
                    name: 'majoo Event',
                    component: asyncRoute(() => import('../pages/majooPreneur/Event')),
                    exact: true,
                },
                {
                    path: 'event',
                    name: 'majoo Event',
                    childRoutes: [
                        {
                            component: asyncRoute(() => import('../pages/majooPreneur/Event')),
                            breadcrumbIgnore: true,
                            exact: true,
                        },
                        {
                            path: 'detail/:id',
                            name: 'Event Detail',
                            component: asyncRoute(() => import('../pages/majooPreneur/Event/EventDetail')),
                        },
                        {
                            path: 'past',
                            name: 'Past Event',
                            component: asyncRoute(() => import('../pages/majooPreneur/Event/EventPast')),
                        },
                    ],
                },
                {
                    path: 'magazine',
                    name: 'Majalah Bulanan',
                    childRoutes: [
                        {
                            component: asyncRoute(() => import('../pages/majooPreneur/Magazine')),
                            breadcrumbIgnore: true,
                            exact: true,
                        },
                        {
                            path: 'detail/:id',
                            name: 'Detail',
                            component: asyncRoute(() => import('../pages/majooPreneur/Magazine/MagazineDetail')),
                        },
                    ],
                },
            ],
        },
        {
            path: 'akunting/account-list',
            name: 'Daftar Akun',
            childRoutes: [
                {
                    component: asyncRoute(() => import('../pages/accountList/V3')),
                    breadcrumbIgnore: true,
                    exact: true,
                },
                {
                    path: 'detail/:id',
                    name: 'Detail Akun',
                    component: asyncRoute(() => import('../pages/accountList/V3/AccoutDetail')),
                },
            ],
        },
        {
            path: 'penjualan',
            name: 'Invoice',
            childRoutes: [
                {
                    path: 'invoice-v2',
                    name: 'Daftar Invoice',
                    childRoutes: [
                        {
                            component: asyncRoute(() => import('../pages/Invoice/InvoiceSales')),
                            breadcrumbIgnore: true,
                            exact: true,
                        },
                    ],
                },
                {
                    path: 'sales-receipt-list-v2',
                    name: 'Daftar Penerimaan Penjualan',
                    childRoutes: [
                        {
                            component: asyncRoute(() => import('../pages/Invoice/SalesReceipt')),
                            breadcrumbIgnore: true,
                            exact: true,
                        },
                    ],
                },
                {
                    path: 'so-list-v2',
                    name: 'Daftar Pesanan Penjualan',
                    childRoutes: [
                        {
                            component: asyncRoute(() => import('../pages/Invoice/SalesOrder')),
                            breadcrumbIgnore: true,
                            exact: true,
                        },
                    ],
                },
                {
                    path: 'sq-list',
                    name: 'Daftar Penawaran Penjualan',
                    childRoutes: [
                        {
                            component: asyncRoute(() => import('../pages/Invoice/SalesQuote')),
                            breadcrumbIgnore: true,
                            exact: true,
                        },
                    ],
                },
                {
                    path: 'do-list-v2',
                    name: 'Daftar Pengiriman Penjualan',
                    childRoutes: [
                        {
                            component: asyncRoute(() => import('../pages/Invoice/DeliveryOrder')),
                            breadcrumbIgnore: true,
                            exact: true,
                        },
                    ],
                },
            ],
        },
        {
            path: 'layanan-pengembangan',
            name: 'Pengembangan Usaha',
            childRoutes: [
                {
                    path: 'kredit-usaha',
                    name: 'Modal Wirausaha',
                    childRoutes: [
                        {
                            component: asyncRoute(() => import('../pages/LayananPengembangan/KreditUsahaV3')),
                            breadcrumbIgnore: true,
                            exact: true,
                        },
                        {
                            path: ':type/:id',
                            name: 'Detail',
                            component: asyncRoute(() => import('../pages/LayananPengembangan/KreditUsaha/V3')),
                        },
                    ],
                },
                {
                    path: 'business-coaching',
                    name: 'Bimbingan Bisnis',
                    component: asyncRoute(() => import('../pages/LayananPengembangan/BusinessCoaching')),
                },
            ],
        },
        // TODO: need validate remove bukalapak karena sudah tutup
        {
            path: 'shopee',
            name: 'Integrasi Shopee',
            childRoutes: [
                {
                    component: asyncRoute(() => import('../pages/Shopee/IntegrationList')),
                    breadcrumbIgnore: true,
                    exact: true,
                },
                {
                    path: 'submission',
                    name: 'Ajukan Integrasi',
                    component: asyncRoute(() => import('../pages/Shopee/Submission')),
                },
                {
                    path: 'initial-setup',
                    name: 'Pengaturan Awal',
                    component: asyncRoute(() => import('../pages/Shopee/InitialSetup')),
                },
                {
                    path: 'integration-setting',
                    name: 'Pengaturan',
                    component: asyncRoute(() => import('../pages/Shopee/IntegrationSetting')),
                },
                {
                    path: 'product-setting',
                    name: 'Pengaturan Produk',
                    childRoutes: [
                        {
                            component: asyncRoute(() => import('../pages/Shopee/ProductSetting')),
                            breadcrumbIgnore: true,
                            exact: true,
                        },
                        {
                            path: ':type/:id',
                            name: 'Detail',
                            component: asyncRoute(() => import('../pages/Shopee/ProductDetail')),
                            exact: true,
                        },
                        {
                            path: ':type',
                            name: 'Detail',
                            component: asyncRoute(() => import('../pages/Shopee/ProductDetail')),
                        },
                    ],
                },
            ],
        },
        {
            path: 'mobile/lainnya',
            name: 'Lainnya',
            component: asyncRoute(() => import('../pages/Mobile/Lainnya')),
            breadcrumbIgnore: true,
        },
        {
            path: 'grabmart',
            name: 'Grabmart',
            childRoutes: [
                {
                    component: asyncRoute(() => import('../pages/Grabmart/ListGrabmartIntegration')),
                    breadcrumbIgnore: true,
                    exact: true,
                },
                {
                    path: 'integration/:id',
                    name: 'Pengajuan Integrasi Grabmart',
                    component: asyncRoute(() => import('../pages/Grabmart/GrabmartIntegration')),
                },
                {
                    path: 'pengaturan-awal-grab/:id',
                    name: 'Pengaturan Awal Grabmart',
                    component: asyncRoute(() => import('../pages/Grabmart/GrabmartInitialSetup')),
                },
                {
                    path: 'pengaturan-grab-merchant/:id',
                    name: 'Pengaturan Grabmart',
                    childRoutes: [
                        {
                            component: asyncRoute(() => import('../pages/Grabmart/GrabmartSetting')),
                            breadcrumbIgnore: true,
                            exact: true,
                        },
                        {
                            path: 'promo',
                            name: 'Promo',
                            childRoutes: [
                                {
                                    component: asyncRoute(() => import('../pages/Grabmart/GrabmartPromo')),
                                    breadcrumbIgnore: true,
                                    exact: true,
                                },
                                {
                                    path: ':type/:idPromo',
                                    name: 'Promo Detail',
                                    component: asyncRoute(() => import('../pages/Grabmart/GrabmartAddPromo')),
                                    breadcrumbIgnore: true,
                                    exact: true,
                                },
                                {
                                    path: ':type',
                                    name: 'Promo Detail',
                                    component: asyncRoute(() => import('../pages/Grabmart/GrabmartAddPromo')),
                                    breadcrumbIgnore: true,
                                },
                            ],
                        },
                        {
                            path: 'product',
                            name: 'product',
                            component: asyncRoute(() => import('../pages/Grabmart/GrabmartProduct')),
                        },
                    ],
                },
            ],
        },
        {
            path: 'gofood',
            name: 'Gofood',
            childRoutes: [
                {
                    component: asyncRoute(() => import('../pages/Gofood/GofoodListIntegration')),
                    breadcrumbIgnore: true,
                    exact: true,
                },
                {
                    path: 'integration/:id',
                    name: 'Pengajuan Integrasi Gofood',
                    component: asyncRoute(() => import('../pages/Gofood/GofoodIntegration')),
                },
                {
                    path: 'initial-setup/:id',
                    name: 'Pengaturan Awal Gofood',
                    component: asyncRoute(() => import('../pages/Gofood/GofoodInitialSetup')),
                },
                {
                    path: 'setting/:id',
                    name: 'Pengaturan Gofood',
                    childRoutes: [
                        {
                            component: asyncRoute(() => import('../pages/Gofood/GofoodSetting')),
                            breadcrumbIgnore: true,
                            exact: true,
                        },
                        {
                            path: 'product',
                            name: 'product',
                            component: asyncRoute(() => import('../pages/Gofood/GofoodProduct')),
                            breadcrumbIgnore: true,
                        },
                    ],
                },
            ],
        },
        {
            path: 'product-marketplace',
            name: 'Product Ecommerce',
            childRoutes: [
                {
                    name: 'Daftar Produk E-Commerce',
                    component: asyncRoute(() => import('../pages/ProductMarketplace/views/ProductList')),
                    exact: true,
                },
                {
                    path: 'product/:type/:productId',
                    name: 'Product',
                    component: asyncRoute(() => import('../pages/ProductMarketplace/views/ProductDetail')),
                    breadcrumbIgnore: true,
                },
            ],
        },
        {
            path: 'marketplace-order',
            name: 'Daftar Pesanan',
            component: asyncRoute(() => import('../pages/MarketplaceOrder/Retina/views/OrderList')),
        },
        {
            path: 'dashboard-keuangan',
            name: 'Dashboard Keuangan',
            component: asyncRoute(() => import('../pages/DashboardFinance/DashboardFinance')),
            pageTitle: 'Dashboard Keuangan',
            breadcrumbIgnore: true,
        },
        {
            path: 'sales-dashboard-keuangan',
            name: 'Dashboard Keuangan',
            component: asyncRoute(() => import('../pages/DashboardFinance/DashboardFinanceDatamart')),
            pageTitle: 'Dashboard Keuangan',
            breadcrumbIgnore: true,
        },
        {
            path: 'toko-supplies/pengaturan',
            name: 'Pengaturan',
            childRoutes: [
                {
                    component: asyncRoute(() => import('../pages/Supplies/Settings')),
                    pageTitle: 'Pengaturan',
                    breadcrumbIgnore: true,
                    exact: true,
                },
                {
                    path: 'produk',
                    name: 'Pengaturan Produk',
                    component: asyncRoute(() => import('../pages/Supplies/Settings/ProductSetting')),
                    pageTitle: 'Pengaturan Produk',
                    breadcrumbIgnore: true,
                },
                {
                    path: 'transaksi',
                    name: 'Pengaturan Transaksi',
                    component: asyncRoute(() => import('../pages/Supplies/Settings/TransactionSetting')),
                    pageTitle: 'Pengaturan Transaksi',
                    breadcrumbIgnore: true,
                },
                {
                    path: 'pengiriman',
                    name: 'Pengaturan Pengiriman',
                    component: asyncRoute(() => import('../pages/Supplies/Settings/DeliverySetting')),
                    pageTitle: 'Pengaturan Pengiriman',
                    breadcrumbIgnore: true,
                },
                {
                    path: 'pembayaran',
                    name: 'Pengaturan Pembayaran',
                    component: asyncRoute(() => import('../pages/Supplies/Settings/PaymentSetting')),
                    pageTitle: 'Pengaturan Pembayaran',
                    breadcrumbIgnore: true,
                },
            ],
        },
        {
            path: 'informasi-update',
            name: 'Informasi Update',
            component: asyncRoute(() => import('../pages/InformationUpdate')),
        },
        {
            path: 'appointment',
            name: 'Appointment',
            childRoutes: [
                {
                    component: asyncRoute(() => import('../pages/Appointment/index')),
                    exact: true,
                },
                {
                    path: 'preset',
                    name: 'Preset Satu Sehat',
                    component: asyncRoute(() => import('../pages/Appointment/PresetSatuSehat')),
                },
            ],
        },
        {
            path: 'medical-practitioner',
            name: 'Tenaga Medis',
            childRoutes: [
                {
                    path: 'practitioner-list',
                    name: 'Daftar Tenaga Medis',
                    component: asyncRoute(() => import('../pages/MedicalPractitioner')),
                },
            ],
        },
        {
            path: 'patient',
            name: 'Pasien',
            childRoutes: [
                {
                    path: 'patient-list',
                    name: 'Daftar Pasien',
                    component: asyncRoute(() => import('../pages/PatientList')),

                },
            ],
        },
        {
            path: 'reminder-transaction-list',
            name: 'Pengingat Transaksi',
            component: asyncRoute(() => import('../pages/Transaction/Reminder')),
        },
        {
            path: 'saldo-awal',
            name: 'Saldo Awal',
            component: asyncRoute(() => import('../pages/BeginningBalance')),
        },
        {
            path: 'master-setting',
            name: 'Pengaturan Master',
            childRoutes: [
                {
                    path: 'position-level',
                    name: 'Position Level',
                    component: asyncRoute(() => import('../pages/MasterSettings/PositionLevel')),
                },
                {
                    path: 'organization',
                    name: 'Organization',
                    component: asyncRoute(() => import('../pages/MasterSettings/Organization')),
                },
                {
                    path: 'employee-type',
                    name: 'Tipe Karyawan',
                    component: asyncRoute(() => import('../pages/MasterSettings/EmployeeType')),
                },
            ],
        },
        {
            path: 'temp',
            name: 'Temporary Menu',
            childRoutes: [
                {
                    path: 'employee-list',
                    name: 'Daftar Karyawan',
                    component: asyncRoute(() => import('../pages/temp/employeeList')),
                },
                {
                    path: 'access-list',
                    name: 'Daftar Hak Akses',
                    component: asyncRoute(() => import('../pages/temp/accessList')),
                },
                {
                    path: 'access-setting',
                    name: 'Pengaturan Hak Akses',
                    component: asyncRoute(() => import('../pages/temp/accessSetting')),
                },
            ],
        },
    ],
};

const authRoutes = {
    path: 'auth',
    breadcrumbIgnore: true,
    childRoutes: [
        {
            path: 'login',
            name: 'login',
            component: asyncRoute(() => import('../pages/Auth/Login')),
        },
        {
            path: 'logout',
            name: 'logout',
            component: asyncRoute(() => import('../pages/Auth/Login')),
        },
        {
            path: 'register',
            name: 'register',
            component: asyncRoute(() => import('../pages/Auth/Register')),
        },
        {
            path: 'change-email',
            name: 'Ubah Email',
            component: asyncRoute(() => import('../pages/Auth/ChangeEmail')),
        },
        {
            path: 'verify',
            name: 'verify',
            component: asyncRoute(() => import('../pages/Auth/Verify')),
        },
        {
            path: 'phone-verification',
            name: 'phoneVerification',
            // component: asyncRoute(() => import('../pages/Auth/PhoneVerification')), //old file
            component: asyncRoute(() => import('../pages/Auth/VerifyPhoneNumber')),
        },
        {
            path: 'verify-number',
            name: 'Verifikasi Nomor HP',
            component: asyncRoute(() => import('../pages/Auth/VerifyNumber')),
        },
        {
            path: 'forgotPassword',
            name: 'forgotPassword',
            component: asyncRoute(() => import('../pages/Auth/ForgotPassword')),
        },
        {
            path: 'resetPassword',
            name: 'resetPassword',
            component: asyncRoute(() => import('../pages/Auth/ResetPassword')),
        },
        {
            path: 'resetPasswordSuccess',
            name: 'resetPasswordSuccess',
            component: asyncRoute(() => import('../pages/Auth/ResetPasswordSuccess')),
        },
    ],
};

const routes = [
    {
        // TODO: remove, di menu sudah tidak dipakai
        path: 'onboardWizard',
        name: 'onboardWizard',
        component: asyncRoute(() => import('../pages/Auth/OnboardWizardRedesign')),
    },
    {
        path: 'quick-survey',
        name: 'Quick Survey',
        component: asyncRoute(() => import('../pages/Auth/QuickSurveyRedesign')),
    },
    {
        // TODO: perlu check ke mobile ada yang manggil/tidak
        path: 'exportInvoice',
        name: 'Export Invoice',
        breadcrumbIgnore: true,
        childRoutes: [
            {
                path: ':id',
                name: 'Export Invoice',
                component: asyncRoute(() => import('../pages/Sales/InvoiceSales/dotMatrixInvoice')),
                breadcrumbIgnore: true,
            },
        ],
    },
    {
        // TODO: perlu check ke mobile ada yang manggil/tidak
        path: 'exportTandaTerima',
        name: 'Export Tanda Terima',
        breadcrumbIgnore: true,
        childRoutes: [
            {
                path: ':id',
                name: 'Export Tanda Terima',
                component: asyncRoute(() => import('../pages/Sales/InvoiceSales/dotMatrixTandaTerima')),
                breadcrumbIgnore: true,
            },
        ],
    },
    {
        path: 'exportQrEmenu',
        name: 'Export QR eMenu',
        breadcrumbIgnore: true,
        childRoutes: [
            {
                path: ':id',
                name: 'Export QR eMenu',
                component: asyncRoute(() => import('../pages/Settings/EMenu/ExportQRPage')),
                breadcrumbIgnore: true,
            },
        ],
    },
    {
        // TODO: Recheck ke mobile apakah di panggil
        path: 'printQr/:id',
        name: 'Print QR',
        component: asyncRoute(() => import('../pages/webOrder/Setting/PrintQR')),
        breadcrumbIgnore: true,
    },
    {
        path: 'businessInfo',
        name: 'Informasi Usaha',
        component: asyncRoute(() => import('../pages/Auth/BusinessInfo')),
    },
    ...[{ ...authRoutes }],
    ...[{ ...mainRoutes }],
];

export default routes;
