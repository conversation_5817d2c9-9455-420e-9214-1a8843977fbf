import isEmpty from 'lodash/isEmpty';
import i18n from 'i18next';
import { clearSession } from '~/services/api/session.util';
import { EXPIRED_ACCOUNT_ERROR_CODE } from '../../utils/api.util';
import { findItemNested, resetDateRangeHelper } from '../../utils/helper';
import { isAllowedSupport } from '../../utils/supportUser';
import userUtils from '../../utils/user.util';
import { accountType } from '../../config/enum';

export const redirectToLogin = (router) => {
    const { pathname } = window.location;
    if (
        pathname !== '/'
        && pathname !== '/auth/logout'
        && pathname !== '/auth/login'
        && pathname !== '/dashboard'
        && pathname !== '/sales-dashboard'
    ) {
        router.push(`/auth/login?redirect=${pathname}`);
    } else {
        router.push('/auth/login');
    }
};

export const defineLoc = (props) => {
    let loc = props.location.pathname;
    loc = loc.substring(1, loc.length);

    // TODO: to check, belum ada perubahan sama sekali
    if (Object.keys(props.params).length > 0) {
        const arrLoc = loc.split('/');
        arrLoc.splice(-2, 2);
        loc = arrLoc.join('/');
    }
    return loc;
};

export const searchMenuByKey = (dataMenu, value, keyToSearch) => {
    /* let result = dataMenu.find(menu => menu[keyToSearch] === value); */

    /* Opsi ketika tidak stay di favorite */
    let result = dataMenu.find((menu) => {
        const cond = !menu.id.includes('-fav');
        return (menu[keyToSearch] === value) && cond;
    });

    if (!result) {
        dataMenu.some((menu) => {
            const { detail } = menu;

            if (detail
                && detail.length > 0
            ) {
                result = searchMenuByKey(detail, value, keyToSearch);

                // return result untuk menentukan looping(.some) lanjut atau stop, bukan untuk mengambil payloadnya
                return result;
            }

            return false;
        });
    }

    return result;
};

export const resetMenu = (props, paramArrMenu, paramPathName, callback) => {
    // TODO: kalau ganti tab tapi ga jadi ganti menu, pas balik ke menu sebelumnya expandnya udah ketutup
    const {
        location: {
            pathname: pName,
        },
        layoutData: {
            menu, activeMenu, activeMenuGroup, expandedMenu,
        },
        onUpdateActiveGroupMenu,
        onUpdateActiveMenu,
        onUpdateExpandedMenu,
    } = props;

    let pathname = pName;
    if (paramPathName !== undefined) pathname = paramPathName;
    const newLoc = pathname.substring(1, pathname.length);

    let arrMenu = menu;

    if (menu.length === 0 && paramArrMenu && paramArrMenu.length > 0) {
        arrMenu = paramArrMenu;
    }

    const activeMenuDetail = searchMenuByKey(arrMenu, newLoc, 'url');

    if (activeMenuDetail
        && activeMenuDetail.id
    ) {
        let listExpandMenu = [];
        let idParent = activeMenuDetail.id_parent;
        let idParentGroup = '';

        for (let i = activeMenuDetail.level - 1; i >= 0; i -= 1) {
            const dataParentMenu = searchMenuByKey(arrMenu, idParent, 'id');
            idParent = dataParentMenu.id_parent;
            if (dataParentMenu.level === 0) {
                idParentGroup = dataParentMenu.id;
            } else {
                listExpandMenu = [{ name: dataParentMenu.name, level: dataParentMenu.level, id: dataParentMenu.id }, ...listExpandMenu];
            }
        }

        if (parseInt(idParentGroup, 10) !== parseInt(activeMenuGroup, 10)) {
            onUpdateActiveGroupMenu(idParentGroup);
        }

        if (parseInt(activeMenuDetail.id, 10) !== parseInt(activeMenu, 10)
            || !expandedMenu.find(item => parseInt(item.id, 10) === parseInt(activeMenuDetail.id_parent, 10))
        ) {
            onUpdateActiveMenu(activeMenuDetail.id, activeMenuDetail.name);
            onUpdateExpandedMenu(listExpandMenu);
        }

        callback(idParentGroup);
    }
};

let activeMenuDiscoveryResults = [];

const processDiscoveringActiveMenu = (menuData, currentId) => {
    const found = menuData.find((menu) => {
        const equalValue = (String(menu.id) === String(currentId));

        if (equalValue) return !!menu;
        if (!menu.detail) return false;
        if (menu.detail.length === 0) return false;

        const childFound = processDiscoveringActiveMenu(menu.detail, currentId);

        return !!childFound;
    });

    if (found) activeMenuDiscoveryResults = [...activeMenuDiscoveryResults, found];

    return found;
};

export const _setGlobalMessage = (props, data) => {
    const { addNotification, setFlashMessage } = props;
    addNotification(data);
    setFlashMessage({});
};

export const setCalendarHandler = (props, start, end, onchange, rangeLimit) => {
    let startDate,
        endDate;

    const { setCalendar } = props;

    // determine startDate and endDate
    if (start && end) {
        startDate = start;
        endDate = end;
    } else {
        ({ calendar: { start: startDate, end: endDate } } = props);
    }

    const { newStartDate, newEndDate, isForceReset } = resetDateRangeHelper(startDate, endDate, rangeLimit);

    if (isForceReset) {
        _setGlobalMessage({
            title: `Maksimal rentang waktu yang diperbolehkan: ${rangeLimit / 30} bulan`,
            message: 'Rentang waktu akan kami sesuaikan dengan pengaturan standar (bulan ini)',
            level: 'warning',
            autoDismiss: 10,
        });
    }

    setCalendar(newStartDate, newEndDate, onchange, rangeLimit);
};

export const getBannerMenuActive = (layoutData) => {
    const { menu, activeMenu } = layoutData;
    let objMenu = {};
    const getBannerById = findItemNested(menu, activeMenu, 'detail');
    if (!isEmpty(getBannerById)) {
        const { page_banner: pageBennerObjMenu } = getBannerById;
        objMenu = pageBennerObjMenu;
        return objMenu;
    }
    return objMenu;
};

export const headerMenuId = {
    TOKO_ONLINE: '29567',
};

export const showToast = (addToast, toastObj, isMobile, isOnline = true) => {
    const { t } = i18n;
    let notification = {
        id: `${toastObj.title} - ${toastObj.message || ''}`,
        preventDuplicate: true,
        ...toastObj,
        message: toastObj.message || '',
        css: {
            ...toastObj.css,
            maxWidth: isMobile ? '80vw' : '576px',
            '& p': {
                whiteSpace: 'unset',
            },
        },
    };
    if (toastObj.errorCode) {
        switch (toastObj.errorCode) {
            case EXPIRED_ACCOUNT_ERROR_CODE:
                notification = {
                    ...notification,
                    id: notification.errorCode,
                    title: t('subscription.expiredSupport.toastTitle', 'Langganan Telah Berakhir!'),
                    level: 'failed',
                    message: t('subscription.expiredSupport.toastDesc', 'Gagal mengambil data outlet. Untuk melanjutkan, aktifkan kembali langganan outlet utama Anda'),
                    preventDuplicate: true,
                };
                clearSession();
                break;
            default:
                break;
        }
    }
    if (!isOnline) {
        notification = {
            ...notification,
            id: 'offline-connection',
            title: t('toast.offline.title', 'Internet Bermasalah'),
            level: 'failed',
            message: t('toast.offline.description', 'Tidak ada koneksi internet, mohon periksa jaringan'),
        };
    }
    addToast({
        id: notification.id, title: notification.title, variant: notification.level === 'error' ? 'failed' : notification.level, description: notification.message, dismissAfter: notification.dismissAfter, preventDuplicate: notification.preventDuplicate, ...notification,
    });
};

export const allOutletDisabled = [
    '/item/service',
    '/item/custom-menu-book',
    '/promosi/promo/invoice',
    '/promosi/promo/product',
    '/pelanggan/special-price-group',
    '/item/ojek-online-price',
    '/item/bundles',
    '/kasbon/daftar-pengajuan-kasbon',
    '/kasbon/daftar-pengajuan-kasbon/history',
    '/kasbon/kasbon-access-list',
    '/laporan/persediaan/ringkasan-persediaan',
    '/pengaturan-bisnis/notification/dashboard',
];

export const outletLoanWhitelist = [
    '/kasbon/kasbon-access-list',
    '/kasbon/daftar-pengajuan-kasbon',
    '/kasbon/daftar-pengajuan-kasbon/history',
];

export const checkFoodOrderAccessibleMenu = (name) => {
    switch (name) {
        case '29782':
        case '30062':
            // case 'toko online':
            return true;
        default:
            return false;
    }
};

export const isSubscriptionIncludesAddon = (supportAlias) => {
    switch (supportAlias) {
        case accountType.STARTER:
        case accountType.MAXIMA_STARTER:
        case accountType.STARTER_BASIC:
        case accountType.ADVANCE:
        case accountType.MAXIMA_ADVANCE:
            return true;
        default:
            return false;
    }
};

export const checkMarketplaceAccessibleMenu = (name) => {
    switch (name) {
        case 'tokopedia':
        case 'bukalapak':
        case 'shopee':
        case 'grabmart':
            return true;
        default:
            return false;
    }
};

export const getContentTranslation = (translations, lang, key) => {
    if (!key) return '';
    const translation = translations[key];
    if (translation) {
        return translation[lang];
    }
    return key;
};
