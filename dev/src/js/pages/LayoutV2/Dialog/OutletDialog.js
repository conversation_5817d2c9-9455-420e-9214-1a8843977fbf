/* eslint-disable react/no-danger */
/* eslint-disable react/forbid-prop-types */
/* eslint-disable import/no-cycle */
import React, {
  cloneElement, useEffect, useState, createRef,
} from 'react';
import {
  Flex,
  Box,
  Heading,
  InputRadio,
  InputRadioGroup,
  InputSearchbox,
  Popover,
  PopoverContent,
  PopoverTrigger,
  Text,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
  Paragraph,
  Button,
} from '@majoo-ui/react';
import PropTypes from 'prop-types';
import moment from 'moment';
import { connect } from 'react-redux';
import { useTranslation } from 'react-i18next';

import { PERMISSIONS } from '~/constants/permissions';
import { GROUP_TYPE } from '~/pages/Settings/BranchGroup/utils';
import { PlusOutline } from '@majoo-ui/icons';
import * as outletSelector from '../../../data/outlets/selectors';
import { filterCabang } from '../../../actions/branchActions';
import userUtil from '../../../utils/user.util';
import { toTitleCase } from '../../../utils/helper';
import PopUpExpiredV2 from '../../Support/components/PopUpExpiredV2';
import { redirectToLogin, allOutletDisabled, outletLoanWhitelist, searchMenuByKey, defineLoc } from '../utils';
import { isHasSession } from '../../../utils/passport';

const SEMUA_OUTLET_ID = 'all';

const getOutletId = outletId => (outletId === SEMUA_OUTLET_ID ? '' : outletId);

const renderOutletList = ({
  selectedHeaderOutlet,
  filteredALLOutlets,
  filteredOutlets,
  filteredOutletGroup,
  refs,
  disabledOutlet,
  disabledOutletGroup,
  ubahOutlet,
  outletList,
  type,
  router,
  t,
  activeMenu,
}) => {
  const outletGroupList = filteredOutletGroup.filter(group => type === 'all' || group.type === type);
  const isProductMenu = activeMenu && activeMenu.is_can_use_group_outlet_product === '1';
  const isReportMenu = activeMenu && activeMenu.is_can_use_group_outlet === '1';
  return type === 'all' || outletGroupList.length > 0 ? (
    <InputRadioGroup
      defaultValue={selectedHeaderOutlet || SEMUA_OUTLET_ID}
      value={selectedHeaderOutlet || SEMUA_OUTLET_ID}
      direction="column"
      gap={5}
      onValueChange={ubahOutlet}
      css={{
        '@sm': {
          ...(filteredOutlets.length > 5
            ? {
              overflowY: 'auto',
            }
            : {}),
        },
      }}
    >
      {filteredALLOutlets
        .filter(_ => type === 'all')
        .map(outlet => (
          <Flex
            key={outlet.id_cabang || SEMUA_OUTLET_ID}
            ref={refs[outlet.id_cabang]}
            css={{ width: '$full', justifyContent: 'space-between' }}
            gap={3}
          >
            <InputRadio
              id={outlet.id_cabang || SEMUA_OUTLET_ID}
              label={outlet.cabang_name}
              value={outlet.id_cabang || SEMUA_OUTLET_ID}
              disabled={disabledOutlet(outlet)}
            />
            <Box
              css={{
                fontSize: 12,
                lineHeight: '25px',
                color: '#585F5F',
                weight: 600,
                minWidth: 60,
                textAlign: 'end',
              }}
            >
              {outletList.filter(x => x.id_cabang !== '').length} Outlet
            </Box>
          </Flex>
        ))}
      {outletGroupList.map(outletGroup => {
        const isDisabled = disabledOutletGroup &&
          (((outletGroup.type !== GROUP_TYPE.PRODUCT && isProductMenu) ||
            (outletGroup.type !== GROUP_TYPE.REPORT && isReportMenu)) || (!isProductMenu && !isReportMenu));
        return ((
          <Flex key={outletGroup.id_cabangs} css={{ width: '$full', justifyContent: 'space-between' }} gap={3}>
            <InputRadio
              id={outletGroup.id_cabangs}
              label={outletGroup.group_name}
              value={outletGroup.id_cabangs}
              disabled={isDisabled}
            />
            <Box
              css={{
                fontSize: 12,
                lineHeight: '25px',
                color: isDisabled ? '$textDisable' : '#585F5F',
                weight: 600,
                minWidth: 60,
                textAlign: 'end',
              }}
            >
              {outletGroup.total_outlet} Outlet
            </Box>
          </Flex>
        ));
      })}
      {filteredOutlets
        .filter(_ => type === 'all')
        .map(outlet => (
          <Flex
            key={outlet.id_cabang}
            ref={refs[outlet.id_cabang]}
            css={{ width: '$full', justifyContent: 'space-between' }}
            gap={3}
          >
            <InputRadio
              id={outlet.id_cabang}
              label={outlet.cabang_name}
              value={outlet.id_cabang}
              disabled={disabledOutlet(outlet)}
            />
          </Flex>
        ))}
    </InputRadioGroup>
  ) : (
    <Flex direction="column" align="center" gap={5}>
      <Paragraph align="center" color="secondary" paragraph="shortContentRegular">
        {t('noDataAvailable', 'Belum ada data yang dapat ditampilkan')}
      </Paragraph>
      <Button
        size="sm"
        onClick={() => router.push('/pengaturan-bisnis/grup-outlet')}
        leftIcon={<PlusOutline color="currentColor" />}
        css={{
          '@md': { width: 'fit-content' },
        }}
      >
        {t('settingOutletGroup', 'Atur Grup')}
      </Button>
    </Flex>
  );
};

const OutletDialog = ({
  children,
  isMobile,
  outletList,
  outletGroupList,
  selectedOutletId,
  selectOutlet,
  closeDrawer,
  router,
  // accountType,
  listOutletLoan,
  disabledOutletGroup,
  selectedHeaderOutlet,
  menuList,
}) => {
  const { t } = useTranslation(['outletDialog', 'translation']);
  const [filteredALLOutlets, setFilteredALLOutlets] = useState(outletList.filter(x => x.id_cabang === ''));
  const [filteredOutlets, setFilteredOutlets] = useState(outletList.filter(x => x.id_cabang !== ''));
  const [filteredOutletGroup, setFilteredOutletGroup] = useState(outletGroupList);
  const [hasExpired, setHasExpired] = useState(false);
  const [msgExpired, setMsgExpired] = useState('');
  const [selectedOutlet, setSelectedOutlet] = useState();
  const [activeMenu, setActiveMenu] = useState();
  const [open, setOpen] = useState(false);

  const [selectedTab, setSelectedTab] = useState('all');

  const isAdmin = userUtil.getLocalConfigByKey('permissionId') === PERMISSIONS.ADMIN;

  const handleSearch = query => {
    if (query) {
      setFilteredALLOutlets(
        outletList.filter(
          item => item.id_cabang === '' && item.cabang_name.toLowerCase().indexOf(query.toLowerCase()) > -1,
        ),
      );
      setFilteredOutlets(
        outletList.filter(
          item => item.id_cabang !== '' && item.cabang_name.toLowerCase().indexOf(query.toLowerCase()) > -1,
        ),
      );
      setFilteredOutletGroup(
        outletGroupList.filter(item => item.group_name.toLowerCase().indexOf(query.toLowerCase()) > -1),
      );
    } else {
      setFilteredALLOutlets(outletList.filter(x => x.id_cabang === ''));
      setFilteredOutlets(outletList.filter(x => x.id_cabang !== ''));
      setFilteredOutletGroup(outletGroupList);
    }
  };

  const ubahOutlet = valueOutletID => {
    handleSearch();
    const accountType = userUtil.getLocalConfigByKey('accountType').replace('ENTERPRISE', 'PRIME');
    const expList = userUtil.getLocalConfigByKey('outletExp');
    const selectedCabang = filteredOutlets.find(x => x.id_cabang === String(getOutletId(valueOutletID)));
    setOpen(false);

    // NOTE: outletId.includes('-') -> ID Group Outlet
    // contoh: 1-2345,3422,4563
    if (valueOutletID !== SEMUA_OUTLET_ID && !valueOutletID.includes('-')) {
      if (Object.hasOwnProperty.call(expList, valueOutletID)) {
        const outletExp = expList[valueOutletID];
        const timeNow = moment().format('YYYY-MM-DD HH:mm:ss');
        const checkWaktu = moment(timeNow).isAfter(outletExp);
        if (checkWaktu) {
          setSelectedOutlet(selectedCabang);

          // RETINA VERSION
          setMsgExpired(
            t(
              'subscription.expiredSupport.desciptionDialog',
              `Masa aktif paket ${accountType && `<b>${toTitleCase(accountType)}</b>`} pada ${selectedCabang.cabang_name ? `<b>${selectedCabang.cabang_name}</b>` : 'ini'
              } telah berakhir sejak <b>${moment(outletExp).format(
                'DD MMMM YYYY',
              )}</b>. Silakan perbarui paket berlangganan anda.`,
              {
                ns: 'translation',
                accountType: `${accountType ? `<b>${toTitleCase(accountType)}</b>` : ''}`,
                outletName: `outlet ${selectedCabang.cabang_name
                  ? `<b>${selectedCabang.cabang_name}</b>`
                  : t('subscription.expiredSupport.thisOutlet', { ns: 'translation' })
                  }`,
                expiredDate: moment(outletExp).format('DD MMMM YYYY'),
              },
            ),
          );
          setHasExpired(true);

          // EXISTING VERSION
          // setMsgExpired(`Masa langganan pada outlet ${(selectedCabang.cabang_name) ? `<b>${selectedCabang.cabang_name}</b>` : 'ini'} telah berakhir pada <b>${moment(outletExp).format('DD MMMM YYYY')}</b>. Silahkan memperbarui langganan outlet anda.`);
          // expiredOutletPopupRef.current.showPopup();
          return;
        }
      }
    }

    let filterBranch = '';
    const getDataGroup = outletGroupList.find(x => x.id_cabangs === valueOutletID);
    filterBranch = getDataGroup ? getDataGroup.branchIdCollection : String(getOutletId(valueOutletID));
    filterBranch = getDataGroup && getDataGroup.type === GROUP_TYPE.PRODUCT ? getDataGroup.default_outlet || filterBranch : filterBranch;
    selectOutlet(filterBranch, String(getOutletId(valueOutletID)));
    if (closeDrawer) closeDrawer();
  };

  const redirectToBuySupport = () => {
    // expiredOutletPopupRef.current.hidePopup(); // if using existing version
    setHasExpired(false);
    router.push(
      selectedOutlet.support_url_alias !== null
        ? `/support/buy?support=${selectedOutlet.support_url_alias}`
        : '/support/buy',
    );
  };

  const refs = filteredOutlets.reduce((acc, value) => {
    acc[value.id_cabang] = createRef();
    return acc;
  }, {});

  const scrollToOutlet = id =>
    refs[id].current.scrollIntoView({
      behavior: 'instant',
      block: 'center',
    });

  const disabledOutlet = outlet => {
    if (outletLoanWhitelist.includes(router.location.pathname))
      return !listOutletLoan.some(x => String(x.outlet_id) === String(outlet.id_cabang));

    return allOutletDisabled.includes(router.location.pathname) && !outlet.id_cabang;
  };

  const getOutletType = () => {
    if (selectedHeaderOutlet.includes('-')) {
      // selected group outlet
      const selectedGroupOutlet = outletGroupList.find(groupOutlet => groupOutlet.id_cabangs === selectedHeaderOutlet);
      return selectedGroupOutlet ? selectedGroupOutlet.type : 'all';
    }
    return 'all';
  }

  useEffect(() => {
    setFilteredALLOutlets(outletList.filter(x => x.id_cabang === ''));
    setFilteredOutlets(outletList.filter(x => x.id_cabang !== ''));
  }, [JSON.stringify(outletList)]);

  useEffect(() => {
    setFilteredOutletGroup(outletGroupList);
  }, [JSON.stringify(outletGroupList)]);

  useEffect(() => {
    if (open && filteredOutlets && filteredOutlets.length > 0 && selectedOutletId) {
      if (refs[selectedOutletId] && refs[selectedOutletId].current) {
        scrollToOutlet(selectedOutletId);
      }
    }
  }, [filteredOutlets, selectedOutletId, open]);

  useEffect(() => {
    const loc = defineLoc({ location: router.location, params: {} });
    const activeMenuDetail = searchMenuByKey(menuList, loc, 'url');
    setActiveMenu(activeMenuDetail);
  }, [router.location]);

  useEffect(() => {
    setSelectedTab(getOutletType());
  }, [selectedHeaderOutlet, outletGroupList]);

  return (
    <React.Fragment>
      <Popover open={open} onOpenChange={setOpen} placement="bottom-start">
        <PopoverTrigger>
          {cloneElement(children, {
            onClick: () => {
              if (!isHasSession()) {
                redirectToLogin(router);
              }
            },
          })}
        </PopoverTrigger>
        <PopoverContent
          isMobile={isMobile}
          style={{
            position: 'fixed',
            top: isMobile ? 0 : 123,
          }}
        >
          <Box
            css={{
              '@sm': { width: '100vw', height: '100vh' },
              '@lg': { width: 324, height: '65vh' },
            }}
          >
            <Box
              css={{
                display: 'flex',
                gap: '$compact',
                flexDirection: 'column',
                background: '$white',
                boxShadow: '$medium',
                padding: 16,
                borderRadius: '$lg',
                height: 'auto',
                overflow: 'unset',
                '@lg': {
                  padding: 24,
                },
              }}
            >
              <Box>
                <Heading
                  heading="sectionTitle"
                  css={{ lineHeight: '24px', marginBottom: '$spacing-03 !important' }}
                >
                  <Flex css={{ width: '$full', justifyContent: 'space-between' }} gap={3}>
                    {t('title', 'Daftar Outlet')}
                    {isAdmin && (
                      <Box
                        css={{
                          fontSize: 14,
                          color: '$primary500',
                          weight: 600,
                          textAlign: 'end',
                          cursor: 'pointer',
                        }}
                        onClick={() => router.push('/pengaturan-bisnis/grup-outlet')}
                      >
                        {t('settingOutletGroup', 'Atur Grup')} &gt;
                      </Box>
                    )}
                  </Flex>
                </Heading>
              </Box>
              <InputSearchbox placeholder={t('placeholder', 'Cari Outlet ...')} onChange={handleSearch} />
              <Tabs
                activationMode="automatic"
                dir="ltr"
                orientation="horizontal"
                defaultValue={selectedTab}
                onValueChange={setSelectedTab}
                style={{
                  boxShadow: 'none',
                }}
              >
                <TabsList css={{ boxShadow: 'none', borderBottom: '1px solid $bgBorder' }}>
                  <TabsTrigger size="sm" value="all">
                    <Text color={selectedTab === 'all' ? 'green' : 'secondary'}>Semua</Text>
                  </TabsTrigger>
                  <TabsTrigger size="sm" value={GROUP_TYPE.REPORT}>
                    <Text color={selectedTab === GROUP_TYPE.REPORT ? 'green' : 'secondary'}>
                      Laporan
                    </Text>
                  </TabsTrigger>
                  <TabsTrigger size="sm" value={GROUP_TYPE.PRODUCT}>
                    <Text color={selectedTab === GROUP_TYPE.PRODUCT ? 'green' : 'secondary'}>
                      Produk
                    </Text>
                  </TabsTrigger>
                </TabsList>
                <Box css={{ maxHeight: 'calc(50vh - 80px)', overflow: 'auto' }}>
                  <TabsContent value="all" style={{ boxShadow: 'none', padding: '24px' }}>
                    {renderOutletList({
                      selectedHeaderOutlet,
                      filteredALLOutlets,
                      filteredOutlets,
                      filteredOutletGroup,
                      refs,
                      disabledOutlet,
                      disabledOutletGroup,
                      ubahOutlet,
                      outletList,
                      type: 'all',
                      router,
                      t,
                      activeMenu,
                    })}
                  </TabsContent>
                  <TabsContent value={GROUP_TYPE.REPORT} style={{ boxShadow: 'none', padding: '24px' }}>
                    {renderOutletList({
                      selectedHeaderOutlet,
                      filteredALLOutlets,
                      filteredOutlets,
                      filteredOutletGroup,
                      refs,
                      disabledOutlet,
                      disabledOutletGroup,
                      ubahOutlet,
                      outletList,
                      type: GROUP_TYPE.REPORT,
                      router,
                      t,
                      activeMenu,
                    })}
                  </TabsContent>
                  <TabsContent value={GROUP_TYPE.PRODUCT} style={{ boxShadow: 'none', padding: '24px' }}>
                    {renderOutletList({
                      selectedHeaderOutlet,
                      filteredALLOutlets,
                      filteredOutlets,
                      filteredOutletGroup,
                      refs,
                      disabledOutlet,
                      disabledOutletGroup,
                      ubahOutlet,
                      outletList,
                      type: GROUP_TYPE.PRODUCT,
                      router,
                      t,
                      activeMenu,
                    })}
                  </TabsContent>
                </Box>
              </Tabs>
            </Box>
          </Box>
        </PopoverContent>
      </Popover>
      {/* RETINA VERSION */}
      {hasExpired && (
        <PopUpExpiredV2
          onCloseEvent={() => setHasExpired(false)}
          router={router}
          isMobile={isMobile}
          description={<div dangerouslySetInnerHTML={{ __html: msgExpired }} />}
          labelConfirm={t('subscription.expiredSupport.updateButton', 'Perpanjang Sekarang', {
            ns: 'translation',
          })}
          onConfirm={redirectToBuySupport}
        />
      )}
    </React.Fragment>
  );
};

OutletDialog.propTypes = {
  children: PropTypes.node.isRequired,
  isMobile: PropTypes.bool,
  outletList: PropTypes.arrayOf(PropTypes.object),
  outletGroupList: PropTypes.arrayOf(PropTypes.object),
  selectOutlet: PropTypes.func,
  // accountType: PropTypes.string,
  closeDrawer: PropTypes.func,
  router: PropTypes.shape({
    push: PropTypes.func,
    location: PropTypes.shape({
      pathname: PropTypes.string,
    }),
  }).isRequired,
  selectedOutletId: PropTypes.string,
  listOutletLoan: PropTypes.arrayOf(PropTypes.object),
  disabledOutletGroup: PropTypes.bool,
  selectedHeaderOutlet: PropTypes.string,
  menuList: PropTypes.arrayOf(PropTypes.shape({})),
};

OutletDialog.defaultProps = {
  isMobile: false,
  outletList: [],
  outletGroupList: [],
  // accountType: null,
  selectedOutletId: SEMUA_OUTLET_ID,
  selectOutlet: () => { },
  closeDrawer: () => { },
  listOutletLoan: [],
  disabledOutletGroup: true,
  selectedHeaderOutlet: '',
  menuList: [],
};

const mapStateToProps = state => ({
  outletList: outletSelector.getListBranchByPrivilege(state.branch.list, state.users.strap.permissionId),
  outletGroupList: state.branch.groupList,
  selectedHeaderOutlet: state.branch.selectedHeaderOutlet,
  selectedOutletId: state.branch.filter,
  // accountType: state && state.accountInfo && state.accountInfo.accountInfoResult && state.accountInfo.accountInfoResult.hak_akses,
  listOutletLoan: state.loan.listOutletLoan,
  menuList: state.layouts.menu,
});

const mapDispatchToProps = dispatch => ({
  selectOutlet: (idCabang, idCabangRadioGroup) => {
    dispatch(filterCabang(idCabang, idCabangRadioGroup));
  },
});

export default connect(mapStateToProps, mapDispatchToProps)(OutletDialog);
