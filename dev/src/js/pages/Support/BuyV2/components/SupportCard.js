import React, { useContext, useMemo } from 'react';
import PropTypes from 'prop-types';
import {
    Flex, Box, Heading, Button, Text, TagStatus, Banner, Separator,
} from '@majoo-ui/react';
import { DiscountTwoTone } from '@majoo-ui/icons';
import { capitalizeFirstLetter, formatDate, formatPrice } from '../utils';
import { ACCOUNT_TYPE, SUPPORT_ALIAS } from '../enum';
import SupportContext from '../context/SupportContext';
import { useAccountExpiration } from '../../../LayoutV2/hooks';

const SupportCard = ({
    support, isProduct, onBuy, isForm,
}) => {
    const {
        marketplaceExpDate, foodorderExpDate, currentSubscription, LANG_DATA,
        TransComponent, LANG_KEY, isMobile, isSupportExpired,
    } = useContext(SupportContext);
    const {
        support_name: supportName,
        support_monthly_annual_price: supportPrice,
        alias,
    } = support;
    const expD = useAccountExpiration({ isFormatted: false });
    const isNeedAddon = [ACCOUNT_TYPE.TRIAL, ACCOUNT_TYPE.STARTER_BASIC, ACCOUNT_TYPE.STARTER, ACCOUNT_TYPE.ADVANCE].includes(currentSubscription) || isSupportExpired;
    const expDate = useMemo(() => {
        if (!isNeedAddon && isProduct) return expD;
        if (alias === SUPPORT_ALIAS.ECOMMERCE) return marketplaceExpDate;
        if (alias === SUPPORT_ALIAS.FOOD_ORDER) return foodorderExpDate;
        return '';
    }, [marketplaceExpDate, foodorderExpDate, expD]);


    const handleOnClick = () => {
        onBuy({ ...support, expDate: (isProduct && !isNeedAddon) ? true : expDate });
    };

    const isDisabled = useMemo(() => (isProduct && !isForm && currentSubscription === ACCOUNT_TYPE.TRIAL), [isForm, isProduct]);

    console.warn({support, isProduct, onBuy, isForm, expDate, isNeedAddon, marketplaceExpDate, foodorderExpDate, currentSubscription, LANG_DATA,
        TransComponent, LANG_KEY, isMobile, isSupportExpired });

    return (
        <Box
            css={{
                width: '$full',
                display: 'flex',
                justifyContent: 'space-between',
                padding: '$cozy',
                borderRadius: '$lg',
                boxShadow: '0px 4px 10px 0px rgba(0, 0, 0, 0.05), 0px 2px 4px 0px rgba(0, 0, 0, 0.05), 0px 1px 1px 0px rgba(0, 0, 0, 0.07)',
                flexDirection: 'column',
                gap: '$spacing-05',
                '@lg': {
                    gap: 0,
                    flexDirection: 'row',
                    alignContent: 'center',
                },
            }}
        >
            <Flex direction="column" gap={3} css={{ flexBasis: '70%' }}>
                <Flex gap={3} css={{ width: '$full', flexDirection: 'column', '@lg': { flexDirection: 'row', alignItems: 'center' } }}>
                    <Heading heading="sectionSubTitle">{capitalizeFirstLetter(supportName)}</Heading>
                    {(isProduct && !expDate && isNeedAddon) && (
                        <Banner variant="success" css={{ padding: '2px 4px', width: 'fit-content', borderRadius: 100 }}>
                            <Flex align="center" gap={2}>
                                <DiscountTwoTone />
                                <Text css={{ '@lg': { whiteSpace: 'noWrap' } }}>
                                    <TransComponent i18nKey={LANG_KEY.ADDITIONAL_SECTION_FREE}>
                                        <strong>GRATIS!!!</strong>
                                        {' '}
                                        untuk paket langganan Advance dan Prime
                                    </TransComponent>
                                </Text>
                            </Flex>
                        </Banner>
                    )}
                    {expDate && (
                        <TagStatus type="success" size="sm" css={{ maxWidth: 'unset', alignItems: 'center' }}>
                            {LANG_DATA.ADDITIONAL_SECTION_ACTIVE(formatDate(expDate))}
                        </TagStatus>
                    )}
                </Flex>
                <Text>
                    {LANG_DATA.SUPPORT_DESCRIPTION[alias]}
                    {/* {showMore ? LANG_DATA.SUPPORT_DESCRIPTION[alias] : `${LANG_DATA.SUPPORT_DESCRIPTION[alias].substring(0, 60)}...`}
                    {isMobile && (<br />)}
                    <span onClick={() => setShowMore(curr => !curr)} style={{ marginLeft: 4, cursor: 'pointer', color: colors.textGreen }}>
                        {showMore ? LANG_DATA.LESS : LANG_DATA.MORE}
                    </span> */}
                </Text>
            </Flex>
            <Separator mobileOnly />
            <Flex justify="between" align="center" gap={3}>
                <Text>
                    <strong>{formatPrice(supportPrice)}</strong>
                    /
                    {isProduct ? LANG_DATA.ADDITIONAL_SECTION_MONTH : LANG_DATA.ADDITIONAL_SECTION_SESSION}
                </Text>
                <Button onClick={handleOnClick} buttonType={expDate ? 'secondary' : 'primary'} disabled={isDisabled}>
                    {(expDate || (isProduct && !isNeedAddon)) ? LANG_DATA.SET : LANG_DATA.SELECT_PACKAGE}
                </Button>
            </Flex>
        </Box>
    );
};

SupportCard.propTypes = {
    support: PropTypes.shape({
        support_name: PropTypes.string,
        support_monthly_annual_price: PropTypes.number,
        support_subtitle: PropTypes.string,
        alias: PropTypes.string,
    }).isRequired,
    onBuy: PropTypes.func,
    isProduct: PropTypes.bool,
    isForm: PropTypes.bool,
};

SupportCard.defaultProps = {
    onBuy: () => { },
    isProduct: false,
    isForm: false,
};

export default SupportCard;
