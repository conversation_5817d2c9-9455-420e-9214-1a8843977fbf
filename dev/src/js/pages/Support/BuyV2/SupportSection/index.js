import React, {
 useContext, useEffect, useMemo, useState,
} from 'react';
import {
    Heading, TabsContent, Flex, ModalDialog,
    DialogClose, Button, Text, Image,
} from '@majoo-ui/react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { get, groupBy } from 'lodash';
import SupportContext from '../context/SupportContext';
import SupportCard from '../components/SupportCard';
import {
    buyPackage,
    getLocationSupportPrice, getOutletHasSupportAddon,
} from '../../../../data/supports';
import {
    ADD_ON_SUPPORT, PAYMENT_METHOD, SUBSCRIPTION_PACKAGE, SUPPORT_ALIAS,
} from '../enum';
import FormAdditionalSubscription from '../components/FormAdditionalSubscription';
import FormEcommerce from '../components/FormEcommerce';
import { catchError } from '../../../../utils/helper';
import {
    chargeCreditCard, creditCardConfirmation, creditCardInput, updateRecurringPayment,
} from '../PaymentMethods';
import { generateIdOrderKirim, getAdditionalSupportService } from '../utils';
import ModalVirtualAccount from '../components/ModalVirtualAccount';
import ModalAutodebit from '../components/ModalAutodebit';

const schema = LANG_DATA => yup.object().shape({
    _alias: yup.string(),
    support_id: yup.number().required(),
    transaction_detail: yup.string(),
    name: yup.string().required(LANG_DATA.FORM_ERROR.ADDITIONAL.NAME),
    email: yup.string().email(LANG_DATA.FORM_ERROR.ADDITIONAL.EMAIL_FORMAT).required(LANG_DATA.FORM_ERROR.ADDITIONAL.EMAIL),
    phone_number: yup.string().when(['_alias'], {
        is: (_alias) => {
            if ([SUPPORT_ALIAS.ECOMMERCE, SUPPORT_ALIAS.FOOD_ORDER].includes(_alias)) {
            return false;
            } return true;
        },
        then: yup.string().required(LANG_DATA.FORM_ERROR.ADDITIONAL.PHONE_NUMBER).typeError(LANG_DATA.FORM_ERROR.ADDITIONAL.PHONE_NUMBER),
        otherwise: yup.string().nullable(true),
    }),
    address: yup.string().when(['_alias'], {
        is: (_alias) => {
            if (_alias === SUPPORT_ALIAS.ECOMMERCE) {
            return false;
            } return true;
        },
        then: yup.string().required(LANG_DATA.FORM_ERROR.ADDITIONAL.ADDRESS).typeError(LANG_DATA.FORM_ERROR.ADDITIONAL.ADDRESS),
        otherwise: yup.string().nullable(true),
    }),
    province_id: yup.string().required(LANG_DATA.FORM_ERROR.ADDITIONAL.PROVINCE),
    city_id: yup.string().required(LANG_DATA.FORM_ERROR.ADDITIONAL.CITY),
    payment_method: yup.string(),
});

const SupportSection = () => {
    const {
        additionalSupportProduct, additionalSupportService, currentSubscription, router,
        idUser, outletOptions, accountInfo, mainOutletDetail, supportKey, addToast,
        showProgress, hideProgress, LANG_DATA, LANG_KEY, TransComponent, getAllBranch,
        paymentInstructionDialogRef, t, isMobile, setTab, onFetchActiveTransaction,
    } = useContext(SupportContext);

    const [openModal, setOpenModal] = useState(false);
    const [openForm, setOpenForm] = useState(false);
    const [isTransacting, setIsTransacting] = useState(false);
    const [additionalPriceOptions, setAdditionalPriceOptions] = useState([]);
    const [selectedSupport, setSelectedSupport] = useState(additionalSupportProduct.length ? additionalSupportProduct[0] : {});
    const [selectedSubscriptionDetail, setSelectedSubscriptionDetail] = useState({ data: [] });
    const [openEcommerce, setOpenEcommerce] = useState(false);
    const [orderID, setOrderID] = useState();
    const [openVirtualAccount, setOpenVirtualAccount] = useState(false);
    const [openAutodebit, setOpenAutodebit] = useState(false);
    const [openCreditCardPayment, setOpenCreditCardPayment] = useState(false);
    const [cardError, setCardError] = useState('');
    const [total, setTotal] = useState(0);
    const [paymentURL, setPaymentURL] = useState('');

    const defaultValuesForm = {
        _outletRow: [],
        payment_method: PAYMENT_METHOD.VIRTUAL_ACCOUNT,
        _transaction_detail: [{ id_outlet: mainOutletDetail.id_cabang, buy_qty: 1 }],
        transaction_detail: '',
        support_id: null,
        name: accountInfo.user_name,
        email: accountInfo.user_email,
        phone_number: mainOutletDetail.cabang_notlp,
        address: mainOutletDetail.cabang_address || null,
        city_id: mainOutletDetail.id_kota,
        province_id: mainOutletDetail.id_provinsi,
        country_id: mainOutletDetail.id_negara,
        additional_price: 0,
        paymentId: '',
        acomodation_fee: 0,
        item_price: 0,
    };

    const hookForm = useForm({
        resolver: yupResolver(schema(LANG_DATA)),
        defaultValues: defaultValuesForm,
    });

    const filteredAdditionalSupportService = useMemo(() => getAdditionalSupportService(additionalSupportService, currentSubscription), [additionalSupportService, currentSubscription]);

    const {
        handleSubmit, setValue, reset, getValues,
    } = hookForm;

    const fetchEcommerceExpiredDate = async () => {
        try {
            const res = await getOutletHasSupportAddon({ alias: 'TOKOONLINE' });
            if (!res.status) throw new Error(res.error);
            const remapOutlet = Object.entries(res.data).map(([key, value]) => {
                const match = outletOptions.find(f => f.value === key);
                if (match) {
                    return {
                        nama_cabang: match.name,
                        exp_date: value,
                        code: '',
                        claim_qty: 1,
                        _key: match.value,
                    };
                }
                return {};
            });
            setValue('_outletRow', remapOutlet);
            setSelectedSubscriptionDetail({ data: remapOutlet });
        } catch (e) {
            addToast({
                title: LANG_DATA.TOAST_ERROR,
                description: catchError(e),
                variant: 'failed',
            });
        }
    };

    const handlePaymentFinishing = async (result) => {
        try {
            const resUpdatePayment = await updateRecurringPayment(result.data);
            if (resUpdatePayment.status) {
                addToast({
                    title: LANG_DATA.TOAST_SUCCESS,
                    description: (
                        <TransComponent i18nKey={LANG_KEY.TOAST_TRANSACTION_SUCCESS}>
                            {{ name: selectedSupport.support_name }}
                        </TransComponent>
                    ),
                    variant: 'success',
                });
            }
        } catch (e) {
            const message = catchError(e);
            addToast({
                title: LANG_DATA.TOAST_ERROR,
                description: message,
                variant: 'failed',
            });
        } finally {
            setTimeout(() => {
                setIsTransacting(false);
                setOpenForm(false);
                setOpenEcommerce(false);
                setOpenAutodebit(false);
                setTab('history');
                getAllBranch();
            }, 3000);
        }
    };

    const generateAdditionalSupportPayload = (data) => {
        switch (selectedSupport.alias) {
            case SUPPORT_ALIAS.WHATSAPP_LAPORAN:
            case SUPPORT_ALIAS.ECOMMERCE:
                return {
                    id: data.support_id,
                    outlets: data._outletRow.map(({ _key: id, claim_qty: qty }) => ({ id, qty })),
                    additional_fee: data.additional_price,
                    accomodation_fee: data.acomodation_fee || 0,
                };
            case SUPPORT_ALIAS.FOOD_ORDER:
            case SUPPORT_ALIAS.TRAINING_DAN_SETUP:
            case SUPPORT_ALIAS.TRAINING_DAN_SETUP_ADVANCE:
            case SUPPORT_ALIAS.TRAINING_DAN_SETUP_PRIME:
            case SUPPORT_ALIAS.SUPPORT_KEDATANGAN:
            case SUPPORT_ALIAS.SUPPORT_KEDATANGAN_ADVANCE:
            case SUPPORT_ALIAS.SUPPORT_KEDATANGAN_PRIME:
                return {
                    id: data.support_id,
                    support: {
                        id: data.support_id,
                        contact: {
                            name: data.name,
                            email: data.email,
                        },
                    },
                    outlets: data._transaction_detail.map(outlet => ({
                        id: outlet.id_outlet,
                        qty: outlet.buy_qty,
                        contact: {
                            name: data.name,
                            email: data.email,
                            phone_number: data.phone_number,
                            address: data.address,
                            city_id: +data.city_id,
                            province_id: +data.province_id,
                            country_id: 107,
                        },
                    })),
                    additional_fee: data.additional_price,
                    accomodation_fee: data.acomodation_fee || 0,
                };
            default:
                return { ...data };
        }
    };

    const nonAutodebitPayment = async (bankId = null) => {
        setOpenVirtualAccount(false);
        showProgress();
        setIsTransacting(true);
        try {
            const orderId = orderID || generateIdOrderKirim(idUser, selectedSupport.support_id);
            if (!orderID) setOrderID(orderId);
            const data = getValues();
            const payload = {
                order_id: orderId,
                payment: {
                    method: data.payment_method,
                    bank_id: bankId,
                },
                package: SUBSCRIPTION_PACKAGE.MONTHLY,
                tnc: 1,
                additional_support: [generateAdditionalSupportPayload(data)],
            };
            const res = await buyPackage(payload);
            if (!get(res, 'data')) throw new Error(res.msg);
            addToast({
                title: LANG_DATA.TOAST_SUCCESS,
                description: (
                    <TransComponent i18nKey={LANG_KEY.TOAST_TRANSACTION_SUCCESS}>
                        {{ name: selectedSupport.support_name }}
                    </TransComponent>
                ),
                variant: 'success',
            });
            if (data.payment_method === PAYMENT_METHOD.VIRTUAL_ACCOUNT) {
                onFetchActiveTransaction();
                paymentInstructionDialogRef.current.handleShowDialog({
                    bill: {
                        id: get(res, 'data.id'),
                        va_expire_date: get(res, 'data.va.exp_date'),
                        total: get(res, 'data.bill'),
                        va_number: get(res, 'data.va.number'),
                        bank_id: bankId,
                    },
                }, () => setTab('history'));
            } else if (get(res, 'data.payment_link')) {
                setTab('history');
                window.open(res.data.payment_link, '_blank');
            }
            setOpenForm(false);
            setOpenEcommerce(false);
        } catch (error) {
            addToast({
                title: LANG_DATA.TOAST_ERROR,
                description: catchError(error),
                variant: 'failed',
            });
            return;
        } finally {
            setIsTransacting(false);
            hideProgress();
        }
    };

    const autodebitPayment = async (cardPayload) => {
        setIsTransacting(true);
        try {
            const orderId = orderID || generateIdOrderKirim(idUser, selectedSupport.support_id);
            if (!orderID) setOrderID(orderId);
            const token = await creditCardInput(cardPayload);
            const data = getValues();
            let ccPayload = {
                order_id: orderId,
                support: data.support_id,
                name: accountInfo.user_name,
                email: accountInfo.user_email,
                tnc: 1,
                package: SUBSCRIPTION_PACKAGE.MONTHLY,
                additional_support: [generateAdditionalSupportPayload(data)],
            };
            ccPayload = Object.assign(ccPayload, {
                card_number: cardPayload.card_number,
                token: token.data,
            });
            const resMidtrans = await chargeCreditCard(ccPayload);
            const redirectURL = resMidtrans.charge.redirect_url;
            const res3DSPayment = await creditCardConfirmation(redirectURL, setPaymentURL, setOpenCreditCardPayment);
            if (!res3DSPayment.data.saved_token_id) {
                res3DSPayment.data.saved_token_id = resMidtrans.data.token;
            }
            handlePaymentFinishing(res3DSPayment);
        } catch (e) {
            setIsTransacting(false);
            const message = catchError(e);
            switch (message) {
                case 'One or more parameters in the payload is invalid.':
                    setCardError(t('errors.invalid', { ns: 'Pengaturan/subscription/modalAutodebit' }));
                    break;
                case 'Card is not authenticated.':
                    setCardError(t('errors.unautenticated', { ns: 'Pengaturan/subscription/modalAutodebit' }));
                    break;
                default:
                    setCardError(message);
                break;
            }
        }
    };

    const requestPayment = async (data) => {
        const orderId = generateIdOrderKirim(idUser, selectedSupport.support_id);
        setOrderID(orderId);
        setTotal(data.total);
        const isAutodebit = data.payment_method === PAYMENT_METHOD.AUTO_DEBIT;
        const isVirtualAccount = data.payment_method === PAYMENT_METHOD.VIRTUAL_ACCOUNT;
        if (isAutodebit) {
            setCardError('');
            setOpenAutodebit(true);
        }
        else if (isVirtualAccount) setOpenVirtualAccount(true);
        else nonAutodebitPayment();
    };

    const onSubmit = () => {
        handleSubmit(requestPayment)();
    };

    const handleOpenForm = (data) => {
        reset(defaultValuesForm);
        setSelectedSupport(data);
        setValue('_alias', data.alias);
        setValue('support_id', data.support_id);
        if (data.expDate) {
            setOpenModal(true);
            return;
        }
        if (data.alias === SUPPORT_ALIAS.ECOMMERCE) {
            fetchEcommerceExpiredDate();
            setOpenEcommerce(true);
        } else setOpenForm(true);
    };

    const fetchAdditionalSubscriptionPrice = async (param) => {
        const payload = {
            package: param,
        };
        try {
            const res = await getLocationSupportPrice(payload);
            if (!res.status) throw new Error(res.msg);
            const priceOptions = groupBy(res.data, 'support_id');
            setAdditionalPriceOptions(priceOptions);
        } catch (e) {
            addToast({
                title: LANG_DATA.TOAST_ERROR,
                description: catchError(e),
                variant: 'failed',
            });
        }
    };

    useEffect(() => {
        fetchAdditionalSubscriptionPrice(supportKey);
    }, [supportKey]);

    if (additionalSupportService.length < 1) return <React.Fragment />;

    return (
        <TabsContent value="support" style={{ boxShadow: 'none' }}>
            <Flex direction="column" gap={5} mt={4}>
                <React.Fragment>
                    <Heading heading="sectionTitle">
                        {LANG_DATA.SUPPORT_PRODUCT}
                    </Heading>
                    {additionalSupportProduct.map(add => (
                        <SupportCard key={add.id} support={add} isProduct onBuy={handleOpenForm} />
                    ))}
                </React.Fragment>
                <Heading heading="sectionTitle">
                    {LANG_DATA.SUPPORT_SERVICE}
                </Heading>
                {filteredAdditionalSupportService.map(add => (
                    <SupportCard key={add.id} support={add} onBuy={handleOpenForm} />
                ))}
            </Flex>
            {openForm && (
                <FormAdditionalSubscription
                    key={`${openForm}-form-additional`}
                    isTransacting={isTransacting}
                    open={openForm}
                    onOpenChange={setOpenForm}
                    detail={selectedSupport}
                    hookForm={hookForm}
                    outletOptions={outletOptions}
                    onConfirm={onSubmit}
                    additionalPriceOptions={additionalPriceOptions}
                    idUser={idUser}
                />
            )}
            {openEcommerce && (
                <FormEcommerce
                    key={`${openEcommerce}-${selectedSupport.support_id}-${selectedSubscriptionDetail.data.length}-ecommerce`}
                    hookForm={hookForm}
                    open={openEcommerce}
                    onOpenChange={setOpenEcommerce}
                    outletOptions={outletOptions}
                    currentSubscription={currentSubscription}
                    selectedSubscription={selectedSupport}
                    selectedSubscriptionDetail={selectedSubscriptionDetail}
                    mainOutletDetail={mainOutletDetail}
                    onConfirm={onSubmit}
                    isTransacting={isTransacting}
                    idUser={idUser}
                    addToast={addToast}
                />
            )}
            {openVirtualAccount && (<ModalVirtualAccount open={openVirtualAccount} onOpenChange={setOpenVirtualAccount} onClick={nonAutodebitPayment} isMobile={isMobile} />)}
            {openAutodebit && (
                <ModalAutodebit onConfirm={autodebitPayment} open={openAutodebit} onOpenChange={setOpenAutodebit} price={total} orderID={orderID} cardError={cardError} isTransacting={isTransacting} />
            )}
            <ModalDialog open={openCreditCardPayment} onOpenChange={setOpenCreditCardPayment}>
                <ModalDialog.Content>
                    <iframe title="title" frameBorder="0" style={{ height: '80vh', width: '100%' }} src={paymentURL} />
                </ModalDialog.Content>
            </ModalDialog>
            {openModal && (
                <ModalDialog open={openModal} onOpenChange={() => setOpenModal(false)}>
                    <ModalDialog.Title>
                        {LANG_DATA.MODAL_SET_SUPPORT_TITLE(selectedSupport.support_name)}
                    </ModalDialog.Title>
                    <ModalDialog.Content>
                        <Flex direction="column" gap={4}>
                            {ADD_ON_SUPPORT[selectedSupport.alias] && ADD_ON_SUPPORT[selectedSupport.alias].map(addOn => (
                                <Flex items="center" justify="between" key={addOn.name} css={{ border: '1px solid $bgBorder', borderRadius: '$lg', padding: '$spacing-04' }}>
                                    <Flex items="center" gap={4}>
                                        <Image width={30} height={30} src={addOn.icon} alt={addOn.name} />
                                        <Text css={{ margin: 'auto !important', fontWeight: 600 }} color="primary">{addOn.name}</Text>
                                    </Flex>
                                    <Button size="sm" onClick={() => router.push(addOn.link)} buttonType="secondary">
                                        {LANG_DATA.OPEN}
                                    </Button>
                                </Flex>
                            ))}
                        </Flex>
                    </ModalDialog.Content>
                    <ModalDialog.Footer>
                        <DialogClose asChild>
                            <Button buttonType="ghost" size="sm">{LANG_DATA.LABEL_CLOSE}</Button>
                        </DialogClose>
                    </ModalDialog.Footer>
                </ModalDialog>
            )}
        </TabsContent>
    );
};

export default SupportSection;
