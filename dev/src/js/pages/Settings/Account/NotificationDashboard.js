import React, { useEffect, useState, useMemo } from 'react';
import { <PERSON>, Card, Heading, Flex, Text, Button, Paragraph } from '@majoo-ui/react';
import { SalesArrangementOutline, PencilSquareOutline, InventoriesOutline, CalendarOutline } from '@majoo-ui/icons';
import { connect } from 'react-redux';
import { useTranslation } from 'react-i18next';
import PropTypes from 'prop-types';
import { colors } from '~/stitches.config';
import CoreHOC from '../../../core/CoreHOC';
import { BannerText } from '../../../components/retina';

const NotificationDashboard = props => {
    const { router, filterBranch, idCabang, branchList, hideProgress } = props;
    const [sbranchList, setBranchList] = useState([]);
    const { t } = useTranslation('Pengaturan/changeNotification');

    useEffect(() => {
        const _branchList = branchList.reduce(
            (prev, next) => [
                ...prev,
                {
                    id: next[0],
                    name: next[1],
                },
            ],
            [],
        );
        setBranchList(_branchList.reverse());

        hideProgress();
    }, [branchList]);

    const selectedBranch = useMemo(() => (!filterBranch ? idCabang : filterBranch), [filterBranch, idCabang]);

    const branchInfo = useMemo(() => {
        const findName = sbranchList.find(x => String(x.id) === String(selectedBranch));
        if (findName) return { name: findName.name, id: findName.id };
        return { name: '', id: '' };
    }, [sbranchList, selectedBranch]);

    return (
        <Card color="dark" responsive css={{ padding: 0 }}>
            <Flex justify="between" css={{ padding: '32px 24px' }}>
                <Heading heading="pageTitle">
                    {t('title', 'Pengaturan Notifikasi')} - {branchInfo.name}
                </Heading>
                <Button buttonType="ghost" onClick={() => {}}>
                    {t('whatsapp_notif_log', 'Log Notifikasi WhatsApp')}
                </Button>
            </Flex>
            <Box css={{ border: '1px solid #EEF0F0', width: '100%' }} />
            <Box css={{ padding: '16px 24px' }}>
                <BannerText css={{ my: 0 }} />
            </Box>
            <Box css={{ padding: '32px 24px' }}>
                <Flex
                    css={{
                        width: '100%',
                        border: '1px solid #EEF0F0',
                        borderRadius: '8px',
                        padding: '16px',
                    }}
                    align="center"
                    justify="between"
                >
                    <Flex align="center" css={{ gap: '16px' }}>
                        <Flex
                            align="center"
                            justify="center"
                            css={{
                                minWidth: '48px',
                                minHeight: '48px',
                                borderRadius: '50%',
                                backgroundColor: '#FAFAFA',
                            }}
                        >
                            <SalesArrangementOutline color="#404545" size={24} />
                        </Flex>
                        <Box>
                            <Text css={{ color: '#272A2A', fontSize: '14px', fontWeight: '600' }}>
                                {t('send_summary', 'Kirim Ringkasan Penjualan')}
                            </Text>
                            <Text css={{ fontSize: '12px', fontWeight: '400' }}>
                                {t('send_summary_d', 'Atur notifikasi untuk ringkasan penjualan Anda')}
                            </Text>
                        </Box>
                    </Flex>
                    <Button buttonType="ghost" onClick={() => router.push('/pengaturan-bisnis/notification')}>
                        {t('set_notif', 'Atur Notifikasi')}
                    </Button>
                </Flex>
                <Flex
                    css={{
                        width: '100%',
                        border: '1px solid #EEF0F0',
                        borderRadius: '8px',
                        padding: '16px',
                        mt: '24px',
                    }}
                    align="center"
                    justify="between"
                >
                    <Flex align="center" css={{ gap: '16px' }}>
                        <Flex
                            align="center"
                            justify="center"
                            css={{
                                minWidth: '48px',
                                minHeight: '48px',
                                borderRadius: '50%',
                                backgroundColor: '#FAFAFA',
                            }}
                        >
                            <PencilSquareOutline color="#404545" size={24} />
                        </Flex>
                        <Box>
                            <Text css={{ color: '#272A2A', fontSize: '14px', fontWeight: '600' }}>
                                {t('send_changed_notif', 'Kirim Notifikasi Perubahan')}
                            </Text>
                            <Text css={{ fontSize: '12px', fontWeight: '400' }}>
                                {t('send_changed_notif_d', 'Atur notifikasi untuk Perubahan pada data Anda')}
                            </Text>
                        </Box>
                    </Flex>
                    <Button
                        buttonType="ghost"
                        onClick={() => router.push(`/pengaturan-bisnis/change-notification?outlet_id=${branchInfo.id}`)}
                    >
                        {t('set_notif', 'Atur Notifikasi')}
                    </Button>
                </Flex>
                <Flex
                    css={{
                        width: '100%',
                        border: '1px solid #EEF0F0',
                        borderRadius: '8px',
                        padding: '16px',
                        mt: '24px',
                    }}
                    align="center"
                    justify="between"
                >
                    <Flex align="center" css={{ gap: '16px' }}>
                        <Flex
                            align="center"
                            justify="center"
                            css={{
                                minWidth: '48px',
                                minHeight: '48px',
                                borderRadius: '50%',
                                backgroundColor: '#FAFAFA',
                            }}
                        >
                            <InventoriesOutline color="#404545" size={24} />
                        </Flex>
                        <Box>
                            <Text css={{ color: '#272A2A', fontSize: '14px', fontWeight: '600' }}>
                                {t('exp_notif', 'Notifikasi Dtok Kadaluwarsa')}
                            </Text>
                            <Text css={{ fontSize: '12px', fontWeight: '400' }}>
                                {t('exp_notif_d', 'Atur notifikasi untuk stok kadaluwarsa')}
                            </Text>
                        </Box>
                    </Flex>
                    <Button
                        buttonType="ghost"
                        onClick={() =>
                            router.push(`/pengaturan-bisnis/expired-notification?outlet_id=${branchInfo.id}`)
                        }
                    >
                        {t('set_notif', 'Atur Notifikasi')}
                    </Button>
                </Flex>

                {/* WhatsApp Notification Section */}
                <Flex
                    gap={5}
                    css={{
                        width: '100%',
                        mt: '$spacing-09',
                        flexDirection: 'column',
                    }}
                >
                    {/* Header Section */}
                    <Flex
                        css={{
                            width: '100%',
                        }}
                        align="center"
                        justify="between"
                    >
                        <Flex gap={2} direction="column" css={{ flex: 1 }}>
                            <Heading color="primary" heading="sectionTitle">
                                {t('whatsapp_integration', 'Kirim Laporan via WhatsApp Terintegrasi')}
                            </Heading>
                            <Text variant="helper">
                                {t(
                                    'whatsapp_integration_desc',
                                    'Otomatisasi pengiriman laporan tutup kasir, penjualan per jam, dan berbagai laporan lain melalui integrasi notifikasi WhatsApp',
                                )}
                            </Text>
                        </Flex>
                        <Flex align="center" gap={4}>
                            <Flex align="center">
                                <Paragraph paragraph="shortContentBold">Rp 50.000</Paragraph>
                                <Paragraph>
                                    /{t('period.month', 'Bulan', { ns: 'translation' }).toLowerCase()}
                                </Paragraph>
                            </Flex>
                            <Button buttonType="primary" size="sm">
                                {t('buy_addon', 'Beli Add-On')}
                            </Button>
                        </Flex>
                    </Flex>

                    {/* Notification Items */}
                    <Flex gap={3} direction="column">
                        {/* Cash Register Report */}
                        <Flex
                            css={{
                                width: '100%',
                                border: '1px solid $bgBorder',
                                borderRadius: '$lg',
                                padding: '$spacing-03 $spacing-05',
                            }}
                            align="center"
                            justify="between"
                        >
                            <Flex align="center" gap={5}>
                                <Flex
                                    align="center"
                                    justify="center"
                                    css={{
                                        minWidth: '48px',
                                        minHeight: '48px',
                                        borderRadius: '50%',
                                        backgroundColor: '$bgGray',
                                    }}
                                >
                                    <SalesArrangementOutline color={colors.bgDark} size={24} />
                                </Flex>
                                <Box>
                                    <Heading heading="sectionSubTitle" color="primary">
                                        {t('cash_register_report', 'Laporan Tutup Kasir')}
                                    </Heading>
                                    <Text variant="helper">
                                        {t(
                                            'cash_register_report_desc',
                                            'Otomatiskan pengiriman laporan tutup kasir via WhatsApp',
                                        )}
                                    </Text>
                                </Box>
                            </Flex>
                            <Button buttonType="ghost" size="sm" disabled={false}>
                                {t('set_notif', 'Atur Notifikasi')}
                            </Button>
                        </Flex>

                        {/* Hourly Sales Report */}
                        <Flex
                            css={{
                                width: '100%',
                                border: '1px solid $bgBorder',
                                borderRadius: '$lg',
                                padding: '$spacing-03 $spacing-05',
                            }}
                            align="center"
                            justify="between"
                        >
                            <Flex align="center" gap={5}>
                                <Flex
                                    align="center"
                                    justify="center"
                                    css={{
                                        minWidth: '48px',
                                        minHeight: '48px',
                                        borderRadius: '50%',
                                        backgroundColor: '$bgGray',
                                    }}
                                >
                                    <SalesArrangementOutline color={colors.bgDark} size={24} />
                                </Flex>
                                <Box>
                                    <Heading heading="sectionSubTitle" color="primary">
                                        {t('hourly_sales_report', 'Laporan Penjualan Per Jam')}
                                    </Heading>
                                    <Text variant="helper">
                                        {t(
                                            'hourly_sales_report_desc',
                                            'Otomatiskan pengiriman laporan penjualan per jam via WhatsApp',
                                        )}
                                    </Text>
                                </Box>
                            </Flex>
                            <Button buttonType="ghost" size="sm" disabled={false}>
                                {t('set_notif', 'Atur Notifikasi')}
                            </Button>
                        </Flex>

                        {/* Attendance Report */}
                        <Flex
                            css={{
                                width: '100%',
                                border: '1px solid $bgBorder',
                                borderRadius: '$lg',
                                padding: '$spacing-03 $spacing-05',
                            }}
                            align="center"
                            justify="between"
                        >
                            <Flex align="center" gap={5}>
                                <Flex
                                    align="center"
                                    justify="center"
                                    css={{
                                        minWidth: '48px',
                                        minHeight: '48px',
                                        borderRadius: '50%',
                                        backgroundColor: '$bgGray',
                                    }}
                                >
                                    <CalendarOutline color={colors.bgDark} size={24} />
                                </Flex>
                                <Box>
                                    <Heading heading="sectionSubTitle" color="primary">
                                        {t('attendance_report', 'Laporan Absensi')}
                                    </Heading>
                                    <Text variant="helper">
                                        {t(
                                            'attendance_report_desc',
                                            'Otomatiskan pengiriman laporan absensi via WhatsApp',
                                        )}
                                    </Text>
                                </Box>
                            </Flex>
                            <Button buttonType="ghost" size="sm" disabled={false}>
                                {t('set_notif', 'Atur Notifikasi')}
                            </Button>
                        </Flex>
                    </Flex>
                </Flex>
            </Box>
        </Card>
    );
};

NotificationDashboard.propTypes = {
    router: PropTypes.shape({
        push: PropTypes.func,
    }).isRequired,
    filterBranch: PropTypes.string,
    idCabang: PropTypes.string,
    branchList: PropTypes.arrayOf(
        PropTypes.oneOfType([
            PropTypes.string,
            PropTypes.number,
            PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.string, PropTypes.number])),
        ]),
    ),
    hideProgress: PropTypes.func,
};

NotificationDashboard.defaultProps = {
    filterBranch: '',
    idCabang: '',
    branchList: [],
    hideProgress: () => {},
};

const mapStateToProps = state => ({
    branchList: state.branch.listOption,
});

export default connect(mapStateToProps, null)(CoreHOC(NotificationDashboard));
