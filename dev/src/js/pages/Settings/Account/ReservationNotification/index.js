import React, { Fragment, useContext, useState } from 'react';
import {
    Box,
    Card,
    Heading,
    Text,
    Flex,
    InputSwitch,
    Button,
    AlertDialog,
    ToastContext,
    InputSelect,
    FormGroup,
    FormLabel,
    Tabs,
    TabsList,
    TabsTrigger,
    FormHelper,
} from '@majoo-ui/react';
import { Controller, useForm } from 'react-hook-form';
import { connect } from 'react-redux';
import {
    ChevronLeftOutline,
} from '@majoo-ui/icons';
import { get } from 'lodash';
import CoreHOC from '../../../../core/CoreHOC';
import { defaultForm, resolver } from './form';
import { useReservationNotification } from './hooks/useReservationNotification';
import { useTranslationHook } from './lang.utils';
import { TAB_VALUE } from './enum';
import { FlexCard } from './utils';
import ConfigTab from './ConfigTab';
import TemplateTab from './TemplateTab';

const ReservationNotification = (props) => {
    const {
        branchList, showProgress, hideProgress, router,
    } = props;
    const toast = useContext(ToastContext);
    const { LANG_DATA, LANG_KEY, TransComponent, currentLang } = useTranslationHook();
    const forms = useForm({ defaultValues: defaultForm, resolver: resolver(LANG_DATA) });
    const { formState: { errors, isSubmitting }, control, watch } = forms;
    const [tab, setTab] = useState(TAB_VALUE.CONFIG);

    const tabs = [
        { value: TAB_VALUE.CONFIG, label: LANG_DATA.FORM.TABS.CONFIG.TITLE },
        { value: TAB_VALUE.TEMPLATE, label: LANG_DATA.FORM.TABS.TEMPLATE.TITLE },
    ];

    const {
        loading,
        handleSetFinalForm,
        handleConfirm,
        show,
        setShow,
        handleChange,
        outletName,
        whatsAppOptions,
    } = useReservationNotification({
        ...props, ...forms, toast, LANG_DATA, LANG_KEY, TransComponent, currentLang,
    });

    return (
        <Card color="dark" responsive css={{ padding: 0 }}>
            <Box css={{ padding: '16px 24px 32px 24px' }}>
                <Button
                    css={{
                        paddingLeft: '0',
                        left: '-8px',
                        mb: '30px',
                        '&:hover': {
                            backgroundColor: 'inherit',
                            outline: 'none',
                        },
                        '> div': {
                            display: 'flex',
                        },
                    }}
                    onClick={() => router.goBack()}
                    buttonType="ghost"
                    leftIcon={<ChevronLeftOutline color="currentColor" />}
                >
                    {LANG_DATA.BACK}
                </Button>
                <Heading heading="pageTitle">
                    {LANG_DATA.TITLE}
                </Heading>
            </Box>
            <Box css={{ border: '1px solid #EEF0F0', width: '100%' }} />
            <Box css={{ padding: '32px 24px', display: 'flex', flexDirection: 'column', gap: '$cozy' }}>
                <FlexCard
                    justify="between"
                    wrap="wrap"
                >
                    <Box>
                        <Flex align="center" css={{ gap: '8px', mb: '4px' }}>
                            <Text
                                css={{ color: '#272A2A', fontSize: '16px', fontWeight: '600' }}
                            >
                                {LANG_DATA.FORM.SEND_NOTIF.TITLE}
                            </Text>
                        </Flex>
                        <Text css={{ fontSize: '14px', fontWeight: '400' }}>
                            {LANG_DATA.FORM.SEND_NOTIF.DESCRIPTION}
                        </Text>
                    </Box>
                    <Box>
                        <InputSwitch
                            checked={watch('is_reservation') === '1'}
                            onCheckedChange={e => handleChange('is_reservation', e ? '1' : '0')}
                            dataOnLabel="ON"
                            dataOffLabel="OFF"
                        />
                    </Box>
                </FlexCard>
                {watch('is_reservation') === '1' && (
                    <Fragment>
                        <FlexCard
                            direction="column"
                            wrap="wrap"
                            gap={4}
                        >
                            <Box>
                                <Flex align="center" css={{ gap: '8px', mb: '4px' }}>
                                    <Text
                                        css={{ color: '#272A2A', fontSize: '16px', fontWeight: '600' }}
                                    >
                                        {LANG_DATA.FORM.SETTING.TITLE}
                                    </Text>
                                </Flex>
                                <Text css={{ fontSize: '14px', fontWeight: '400' }}>
                                    {LANG_DATA.FORM.SETTING.DESCRIPTION}
                                </Text>
                            </Box>
                            <Box>
                                <FormGroup responsive="input">
                                    <FormLabel>{LANG_DATA.FORM.SETTING.LABEL}</FormLabel>
                                    <Controller
                                        name="reservation_setting.wa_sender_identifier"
                                        control={control}
                                        render={({ field: { value, onChange } }) => (
                                            <InputSelect
                                                isInvalid={!!get(errors, 'reservation_setting.wa_sender_identifier')}
                                                value={whatsAppOptions.find(x => String(x.value) === String(value))}
                                                option={whatsAppOptions}
                                                onChange={x => onChange(x.value)}
                                            />
                                        )}
                                    />
                                    {get(errors, 'reservation_setting.wa_sender_identifier') && <FormHelper error>{get(errors, 'reservation_setting.wa_sender_identifier.message')}</FormHelper>}
                                </FormGroup>
                            </Box>
                        </FlexCard>
                        <Tabs
                            defaultValue={tab}
                            css={{ boxShadow: 'none' }}
                            onValueChange={val => setTab(val)}
                        >
                            <TabsList css={{ boxShadow: 'none' }}>
                                {tabs.map(({ value, label }) => (
                                    <TabsTrigger key={value} value={value}>
                                        <Text color={tab === value ? 'green' : 'primary'}>{label}</Text>
                                    </TabsTrigger>
                                ))}
                            </TabsList>
                            <ConfigTab
                                LANG_DATA={LANG_DATA}
                                watch={watch}
                                control={control}
                                handleChange={handleChange}
                            />
                            <TemplateTab
                                LANG_DATA={LANG_DATA}
                                watch={watch}
                                control={control}
                                handleChange={handleChange}
                            />
                        </Tabs>
                    </Fragment>
                )}
                <Flex justify="end">
                    <Button
                        onClick={handleSetFinalForm}
                        css={{ mt: '30px' }}
                        disabled={!Array.isArray(branchList) || loading || isSubmitting}
                        buttonType="primary"
                    >
                        {LANG_DATA.SAVE}
                    </Button>
                </Flex>
            </Box>
            {show && (
                <AlertDialog
                    title={LANG_DATA.MODAL_CONFIRMATION.TITLE}
                    description={(
                        <TransComponent i18nKey={LANG_KEY.MODAL_CONFIRMATION.DESCRIPTION}>
                            {{ outlet: outletName }}
                        </TransComponent>
                    )}
                    disabledButton={isSubmitting}
                    hideCloseButton={isSubmitting}
                    open={show}
                    onCancel={() => setShow(false)}
                    onConfirm={() => handleConfirm()}
                    labelCancel={LANG_DATA.MODAL_CONFIRMATION.CANCEL}
                    labelConfirm={LANG_DATA.MODAL_CONFIRMATION.SAVE}
                />
            )}
        </Card>
    );
};

const mapStateToProps = state => ({
    branchList: state.branch.list,
});

export default connect(mapStateToProps)(CoreHOC(ReservationNotification));
