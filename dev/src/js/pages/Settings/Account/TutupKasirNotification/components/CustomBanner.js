import React, { useState, Fragment } from 'react';
import PropTypes from 'prop-types';
import {
    Banner, BannerClose, Paragraph, Flex,
} from '@majoo-ui/react';
import { CircleInfoFilled } from '@majoo-ui/icons';

const CustomBanner = ({ children }) => {
    const [isShow, setShow] = useState(true);
    if (!isShow) return (<Fragment />);
    return (
        <Banner variant="info" bannerBlock onRemove={() => setShow(false)}>
            <Flex justify="between" align="center" gap={4} css={{ width: '$full' }}>
                <Flex gap={4} align="start" wrap="wrap" css={{ width: '$full' }}>
                    <CircleInfoFilled color="#34ACF6" />
                    <Paragraph css={{ width: '80%' }}>
                        {children}
                    </Paragraph>
                </Flex>
                <BannerClose />
            </Flex>
        </Banner>
    );
};

CustomBanner.propTypes = {
    children: PropTypes.node.isRequired,
};

export default CustomBanner;
