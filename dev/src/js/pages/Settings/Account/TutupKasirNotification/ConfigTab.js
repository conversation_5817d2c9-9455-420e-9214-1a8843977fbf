import React, { Fragment } from 'react';
import {
    TabsContent,
    Box,
    Flex,
    Text,
    Separator,
    InputSwitch,
    InputSelectTag,
    FormGroup,
    FormLabel,
    Tooltip,
} from '@majoo-ui/react';
import { CircleInfoOutline } from '@majoo-ui/icons';
import { Controller } from 'react-hook-form';
import { FlexCard, timeOptions } from './utils';
import { TAB_VALUE } from './enum';

const ConfigTab = ({ LANG_DATA, watch, control, handleChange }) => {

    return (
        <TabsContent value={TAB_VALUE.CONFIG} css={{ boxShadow: 'none' }}>
            <FlexCard
                direction="column"
                wrap="wrap"
                gap={4}
                mt={4}
            >
                <Box>
                    <Flex align="center" css={{ gap: '8px', mb: '4px' }}>
                        <Text
                            css={{ color: '#272A2A', fontSize: '16px', fontWeight: '600' }}
                        >
                            {LANG_DATA.FORM.TABS.CONFIG.TITLE2}
                        </Text>
                    </Flex>
                    <Text css={{ fontSize: '14px', fontWeight: '400' }}>
                        {LANG_DATA.FORM.TABS.CONFIG.DESCRIPTION}
                    </Text>
                </Box>
                <FlexCard
                    justify="between"
                    wrap="wrap"
                >
                    <Box>
                        <Flex align="center" css={{ gap: '8px', mb: '4px' }}>
                            <Text
                                css={{ color: '#272A2A', fontSize: '16px', fontWeight: '600' }}
                            >
                                {LANG_DATA.FORM.FIELD.CREATE_RESERVATION.TITLE}
                            </Text>
                        </Flex>
                        <Text css={{ fontSize: '14px', fontWeight: '400' }}>
                            {LANG_DATA.FORM.FIELD.CREATE_RESERVATION.DESCRIPTION}
                        </Text>
                    </Box>
                    <InputSwitch
                        checked={watch('reservation_setting.create_reservation_notif')}
                        onCheckedChange={e => handleChange('reservation_setting.create_reservation_notif', e)}
                        dataOnLabel="ON"
                        dataOffLabel="OFF"
                    />
                </FlexCard>
                <FlexCard
                    justify="between"
                    wrap="wrap"
                >
                    <Box>
                        <Flex align="center" css={{ gap: '8px', mb: '4px' }}>
                            <Text
                                css={{ color: '#272A2A', fontSize: '16px', fontWeight: '600' }}
                            >
                                {LANG_DATA.FORM.FIELD.UPDATE_RESERVATION.TITLE}
                            </Text>
                        </Flex>
                        <Text css={{ fontSize: '14px', fontWeight: '400' }}>
                            {LANG_DATA.FORM.FIELD.UPDATE_RESERVATION.DESCRIPTION}
                        </Text>
                    </Box>
                    <InputSwitch
                        checked={watch('reservation_setting.update_reservation_notif')}
                        onCheckedChange={e => handleChange('reservation_setting.update_reservation_notif', e)}
                        dataOnLabel="ON"
                        dataOffLabel="OFF"
                    />
                </FlexCard>
                <FlexCard
                    direction="column"
                    wrap="wrap"
                    gap={4}
                >
                    <Flex justify="between" wrap="wrap" css={{ width: '$full' }}>
                        <Box>
                            <Flex align="center" css={{ gap: '8px', mb: '4px' }}>
                                <Text
                                    css={{ color: '#272A2A', fontSize: '16px', fontWeight: '600' }}
                                >
                                    {LANG_DATA.FORM.FIELD.REMINDER_RESERVATION.TITLE}
                                </Text>
                            </Flex>
                            <Text css={{ fontSize: '14px', fontWeight: '400' }}>
                                {LANG_DATA.FORM.FIELD.REMINDER_RESERVATION.DESCRIPTION}
                            </Text>
                        </Box>
                        <InputSwitch
                            checked={watch('reservation_setting.reminder_reservation_notif')}
                            onCheckedChange={e => {
                                handleChange('reservation_setting.reminder_reservation_notif', e);
                                if (!e) {
                                    handleChange('reservation_setting.delivery_time', []);
                                    handleChange('reservation_setting.change_setup_reservation_notif', e);
                                    handleChange('reservation_setting.button_reminder_customer', e);
                                }
                            }}
                            dataOnLabel="ON"
                            dataOffLabel="OFF"
                        />
                    </Flex>
                    {watch('reservation_setting.reminder_reservation_notif') && (
                        <Fragment>
                            <Separator />
                            <FormGroup responsive="input">
                                <Flex direction="column" gap={2}>
                                    <FormLabel css={{ '> span': { color: '$textPrimary', fontSize: '$sectionSubTitle', fontWeight: 600 } }}>{LANG_DATA.FORM.FIELD.TIME_RESERVATION.TITLE}</FormLabel>
                                    <Text color="primary" variant="helper">{LANG_DATA.FORM.FIELD.TIME_RESERVATION.DESCRIPTION}</Text>
                                </Flex>
                                <Controller
                                    name="reservation_setting.delivery_time"
                                    control={control}
                                    render={({ field: { value, onChange } }) => (
                                        <InputSelectTag
                                            value={value}
                                            option={timeOptions(LANG_DATA)}
                                            onChange={(x) => onChange(x)}
                                            showSelectAll={false}
                                        />
                                    )}
                                />
                            </FormGroup>
                            <FormGroup responsive="input">
                                <FormLabel>
                                    <span style={{ display: 'flex', alignItems: 'center', gap: "6px" }}>
                                        <span>{LANG_DATA.FORM.FIELD.CHANGE_RESERVATION.TITLE}</span>
                                        <Tooltip
                                            align="center"
                                            side="top"
                                            label={(
                                                <Text variant="helper">
                                                    {LANG_DATA.FORM.FIELD.CHANGE_RESERVATION.DESCRIPTION}
                                                </Text>
                                            )}
                                        >
                                            <CircleInfoOutline color="currentColor" />
                                        </Tooltip>
                                    </span>
                                </FormLabel>
                                <Controller
                                    name="reservation_setting.change_setup_reservation_notif"
                                    control={control}
                                    render={({ field: { value, onChange } }) => (
                                        <Flex gap={4}>
                                            <InputSwitch
                                                checked={value}
                                                onCheckedChange={e => onChange(e)}
                                                dataOnLabel="ON"
                                                dataOffLabel="OFF"
                                            />
                                            <Text color="primary">{LANG_DATA.FORM.FIELD.CHANGE_RESERVATION.DESCRIPTION}</Text>
                                        </Flex>
                                    )}
                                />
                            </FormGroup>
                            <FormGroup responsive="input">
                                <FormLabel>{LANG_DATA.FORM.FIELD.BUTTON_REMINDER_RESERVATION.TITLE}</FormLabel>
                                <Controller
                                    name="reservation_setting.button_reminder_customer"
                                    control={control}
                                    render={({ field: { value, onChange } }) => (
                                        <Flex gap={4}>
                                            <InputSwitch
                                                checked={value}
                                                onCheckedChange={e => onChange(e)}
                                                dataOnLabel="ON"
                                                dataOffLabel="OFF"
                                            />
                                            <Text color="primary">{LANG_DATA.FORM.FIELD.BUTTON_REMINDER_RESERVATION.DESCRIPTION}</Text>
                                        </Flex>
                                    )}
                                />
                            </FormGroup>
                        </Fragment>
                    )}
                </FlexCard>
            </FlexCard>
        </TabsContent>
    );
};

export default ConfigTab;
