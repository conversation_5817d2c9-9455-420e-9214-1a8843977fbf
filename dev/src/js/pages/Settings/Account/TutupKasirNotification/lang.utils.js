import React from 'react';
import { Trans, useTranslation } from 'react-i18next';

export const useTranslationHook = () => {
    const { t, ready, i18n } = useTranslation(['Settings/reservationNotification', 'translation']);

    const TransComponent = ({ i18nKey, children }) => (
        <Trans t={t} i18nKey={i18nKey}>
            {children}
        </Trans>
    );

    const LANG_KEY = {
        MODAL_CONFIRMATION: {
            DESCRIPTION: 'modal_confirmation.description',
        },
        TOAST: {
            SUCCESS: 'toast.success',
        },
    };

    const retval = {
        LANG_KEY,
        LANG_DATA: {
            TOAST_SUCCESS: t('translation:toast.success'),
            TOAST_ERROR: t('translation:toast.error'),
            TOAST_SOMETHING_WRONG: t('translation:toast.somethingWrong'),
            TITLE: t('title', 'Notifikasi Reservasi'),
            BACK: t('back', 'Kemba<PERSON>'),
            PERIOD_DAY: t('translation:period.day'),
            FORM: {
                SEND_NOTIF: {
                    TITLE: t('form.send_notif.title', 'Kirim Notifikasi Reservasi'),
                    DESCRIPTION: t('form.send_notif.description', 'Notifikasi akan dikirim sesuai pengaturan notifikasi reservasi'),
                },
                SETTING: {
                    TITLE: t('form.setting.title', 'Pengaturan Nomor Notifikasi'),
                    LABEL: t('form.setting.label', 'Nomor Pengirim'),
                    DESCRIPTION: t('form.setting.description', 'Atur nomor yang digunakan untuk mengirim notifikasi reservasi'),
                    ERROR: t('form.setting.error', 'Nomor Pengirim wajib diisi'),
                },
                TABS: {
                    CONFIG: {
                        TITLE: t('form.tabs.config.title', 'Konfigurasi Notifikasi'),
                        TITLE2: t('form.tabs.config.title2', 'Pengaturan Jenis Pesan'),
                        DESCRIPTION: t('form.tabs.config.description', 'Atur notifikasi yang akan dikirimkan sesuai dengan kebutuhan'),
                    },
                    TEMPLATE: {
                        TITLE: t('form.tabs.template.title', 'Template Pesan'),
                    },
                },
                FIELD: {
                    CREATE_RESERVATION: {
                        TITLE: t('form.field.createReservation.title', 'Notifikasi Pembuatan Reservasi'),
                        DESCRIPTION: t('form.field.createReservation.description', 'Notifikasi otomatis dikirim ketika reservasi berhasil dibuat'),
                    },
                    UPDATE_RESERVATION: {
                        TITLE: t('form.field.updateReservation.title', 'Notifikasi Perubahan Reservasi'),
                        DESCRIPTION: t('form.field.updateReservation.description', 'Notifikasi otomatis dikirim ketika reservasi berhasil diubah'),
                    },
                    REMINDER_RESERVATION: {
                        TITLE: t('form.field.reminderReservation.title', 'Notifikasi Pengingat Reservasi'),
                        DESCRIPTION: t('form.field.reminderReservation.description', 'Notifikasi otomatis dikirim mendekati batas waktu reservasi'),
                    },
                    TIME_RESERVATION: {
                        TITLE: t('form.field.timeReservation.title', 'Waktu Pengiriman'),
                        DESCRIPTION: t('form.field.timeReservation.description', 'Atur waktu pengiriman sebelum waktu reservasi berlangsung'),
                    },
                    CHANGE_RESERVATION: {
                        TITLE: t('form.field.changeReservation.title', 'Perubahan Notifikasi'),
                        DESCRIPTION: t('form.field.changeReservation.description', 'Aktifkan perubahan terhadap waktu pengiriman'),
                    },
                    BUTTON_REMINDER_RESERVATION: {
                        TITLE: t('form.field.buttonReminderReservation.title', 'Tombol Ingatkan Pelanggan'),
                        DESCRIPTION: t('form.field.buttonReminderReservation.description', 'Aktifkan akses outlet untuk mengirim notifikasi pengingat ke pelanggan'),
                    },
                    TYPE: {
                        TITLE: t('form.field.type.title', 'Tipe Notifikasi'),
                        TITLE2: t('form.field.type.title2', 'Pilih Notifikasi'),
                        TITLE3: t('form.field.type.title3', 'Kondisi Reservasi'),
                        DESCRIPTION: t('form.field.type.description', 'Pilih template notifikasi yang akan ditampilkan'),
                        CREATE: t('form.field.type.create', 'Notifikasi Pembuatan'),
                        UPDATE: t('form.field.type.update', 'Notifikasi Perubahan'),
                        REMINDER: t('form.field.type.reminder', 'Notifikasi Pengingat'),
                        REPEAT: t('form.field.type.repeat', 'Reservasi Berulang'),
                        NO_REPEAT: t('form.field.type.noRepeat', 'Tidak Berulang'),
                    },
                    DISPLAY: {
                        TITLE: t('form.field.display.title', 'Tampilan Pesan'),
                        BANNER: t('form.field.display.banner', 'Untuk saat ini, Majoo menyediakan 10 varian pesan yang dikirim secara acak untuk menghindari potensi spam.'),
                        VARIATION: t('form.field.display.variation', 'Variasi Pesan'),
                        OUT_OF: t('form.field.display.outOf', 'outOf'),
                    },
                },
            },
            MODAL_CONFIRMATION: {
                TITLE: t('modal_confirmation.title', ''),
                DESCRIPTION: t('modal_confirmation.description', ''),
                CANCEL: t('translation:label.cancel', 'Batal'),
                SAVE: t('translation:label..save', 'Simpan'),
            },
            TOAST: {
                SUCCESS: t('toast.success'),
                FAILED: {
                    ACCESS_RIGHT: t('toast.failed.access_right'),
                    GET_DATA: t('toast.failed.get_data'),
                    SAVE: t('toast.failed.save'),
                },
            },
            SAVE: t('translation:label.save'),
        },
        TransComponent,
        ready,
        currentLang: i18n.language,
    };

    return retval;
};
