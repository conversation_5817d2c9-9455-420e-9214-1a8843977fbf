import React from 'react';
import {
    Box,
    Card,
    Heading,
    Text,
    Flex,
    InputSwitch,
    Button,
    AlertDialog,
} from '@majoo-ui/react';
import { useForm } from 'react-hook-form';
import { connect } from 'react-redux';
import {
    ChevronLeftOutline,
} from '@majoo-ui/icons';
import { Trans, useTranslation } from 'react-i18next';
import CoreHOC from '../../../../core/CoreHOC';
import { defaultForm, resolver } from './form';
import { useTutupKasirNotification } from './hooks/useTutupKasirNotification';
import { FlexCard } from './utils';

const TutupKasirNotification = (props) => {
    const {
        branchList, showProgress, hideProgress, router,
    } = props;
    const { t } = useTranslation('Pengaturan/changeNotification');
    const forms = useForm({ defaultValues: defaultForm, resolver: resolver(t) });
    const { formState: { errors, isSubmitting }, control, watch } = forms;

    const {
        loading,
        handleSetFinalForm,
        handleConfirm,
        show,
        setShow,
        handleChange,
    } = useTutupKasirNotification({
        ...props, ...forms
    });

    return (
        <Card color="dark" responsive css={{ padding: 0 }}>
            <Box css={{ padding: '16px 24px 32px 24px' }}>
                <Button
                    css={{
                        paddingLeft: '0',
                        left: '-8px',
                        mb: '30px',
                        '&:hover': {
                            backgroundColor: 'inherit',
                            outline: 'none',
                        },
                        '> div': {
                            display: 'flex',
                        },
                    }}
                    onClick={() => router.goBack()}
                    buttonType="ghost"
                    leftIcon={<ChevronLeftOutline color="currentColor" />}
                >
                    {t('back')}
                </Button>
                <Heading heading="pageTitle">
                    {t('title')}
                </Heading>
            </Box>
            <Box css={{ border: '1px solid #EEF0F0', width: '100%' }} />
            <Box css={{ padding: '32px 24px', display: 'flex', flexDirection: 'column', gap: '$cozy' }}>
                <FlexCard
                    justify="between"
                    wrap="wrap"
                >
                    <Box>
                        <Flex align="center" css={{ gap: '8px', mb: '4px' }}>
                            <Text
                                css={{ color: '#272A2A', fontSize: '16px', fontWeight: '600' }}
                            >
                                {t('form.sendNotif.title')}
                            </Text>
                        </Flex>
                        <Text css={{ fontSize: '14px', fontWeight: '400' }}>
                            {t('form.sendNotif.description')}
                        </Text>
                    </Box>
                    <Box>
                        <InputSwitch
                            checked={watch('is_reservation') === '1'}
                            onCheckedChange={e => handleChange('is_reservation', e ? '1' : '0')}
                            dataOnLabel="ON"
                            dataOffLabel="OFF"
                        />
                    </Box>
                </FlexCard>
                <Flex justify="end">
                    <Button
                        onClick={handleSetFinalForm}
                        css={{ mt: '30px' }}
                        disabled={!Array.isArray(branchList) || loading || isSubmitting}
                        buttonType="primary"
                    >
                        {t('save')}
                    </Button>
                </Flex>
            </Box>
            {show && (
                <AlertDialog
                    title={t('modalConfirmation.title')}
                    description={(
                        <Trans t={t} i18nKey="modalConfirmation.description" defaults="Apakah Anda yakin ingin menyimpan pengaturan ini?" />
                    )}
                    disabledButton={isSubmitting}
                    hideCloseButton={isSubmitting}
                    open={show}
                    onCancel={() => setShow(false)}
                    onConfirm={() => handleConfirm()}
                    labelCancel={t('modalConfirmation.cancel')}
                    labelConfirm={t('modalConfirmation.save')}
                />
            )}
        </Card>
    );
};

const mapStateToProps = state => ({
    branchList: state.branch.list,
});

export default connect(mapStateToProps)(CoreHOC(TutupKasirNotification));
