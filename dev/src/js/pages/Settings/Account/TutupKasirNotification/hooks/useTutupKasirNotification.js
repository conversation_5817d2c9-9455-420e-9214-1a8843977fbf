import React, { useEffect, useState, useMemo } from 'react';
import { Text } from '@majoo-ui/react';
import { catchError } from '../../../../../utils/helper';
import * as notificationApi from '../../../../../data/notifications';
import * as whatsAppService from '../../../../../data/whatsAppSetting';

export const useTutupKasirNotification = (props) => {
    const {
        filterBranch,
        showProgress,
        hideProgress,
        idCabang,
        reset,
        setValue,
        clearErrors,
        trigger,
        formState: { errors },
        getValues,
        branchList,
    } = props;
    const [loading, setLoading] = useState(false);
    const [whatsAppOptions, setWhatsAppOptions] = useState([]);
    const [form, setForm] = useState({
        data: {},
        is_reservation: '0',
        reservation_setting: {
            wa_sender_identifier: '',
            create_reservation_notif: false,
            update_reservation_notif: false,
            reminder_reservation_notif: false,
            delivery_time: [],
            change_setup_reservation_notif: false,
            button_reminder_customer: false,
        },
        template: {
            template_type: 'Notifikasi pembuatan',
            template_condition: 'Tidak berulang',
        },
    });
    const [show, setShow] = useState(false);
    const outletId = filterBranch || idCabang;
    const outletName = useMemo(() => {
        if (outletId && branchList.length) return branchList.find(branch => branch.id_cabang === outletId).cabang_name;
        return '';
    }, [outletId, branchList]);

    const fetchSettings = async () => {
       
    };

    const fetchWhatsAppList = async () => {
       
    };

    useEffect(() => {
        if (outletId) {
            fetchSettings();
            fetchWhatsAppList();
        }
    }, [outletId]);

    const handleChange = (key, value) => {
    };

    const handleSetFinalForm = async () => {
    };

    const handleConfirm = async () => {
    
    };

    return {
        ...form,
        loading,
        handleSetFinalForm,
        handleConfirm,
        show,
        setShow,
        handleChange,
        outletName,
        whatsAppOptions,
    };
};
