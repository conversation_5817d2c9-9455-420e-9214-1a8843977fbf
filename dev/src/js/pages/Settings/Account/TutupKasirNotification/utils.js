import { Flex } from '@majoo-ui/react';
import { styled } from '../../../../stitches.config';

export const FlexCard = styled(Flex, {
    width: '$full',
    border: '1px solid $bgBorder',
    borderRadius: '$lg',
    padding: '$spacing-06 $spacing-05',
});

export const  timeOptions = (LANG_DATA) => ([
    { id: '-1 day', name: `1 ${LANG_DATA.PERIOD_DAY}` },
    { id: '-2 day', name: `2 ${LANG_DATA.PERIOD_DAY}` },
    { id: '-3 day', name: `3 ${LANG_DATA.PERIOD_DAY}` },
    { id: '-4 day', name: `4 ${LANG_DATA.PERIOD_DAY}` },
    { id: '-5 day', name: `5 ${LANG_DATA.PERIOD_DAY}` },
    { id: '-6 day', name: `6 ${LANG_DATA.PERIOD_DAY}` },
    { id: '-7 day', name: `7 ${LANG_DATA.PERIOD_DAY}` },
]);

export const mappingValueContent = (string, isWa = false) => {
    let formattedString = string;
    formattedString = formattedString.replace('{{.Data.TemplateData.nama}}', isWa ? 'Delia Aruna' : '<strong>[Nama Pelanggan]</strong>');
    formattedString = formattedString.replace('{{.Data.TemplateData.link_pdf}}', isWa ? '<a href="#" style="color:blue;">link</a>' : '<strong>[Link PDF]</strong>');
    formattedString = formattedString.replace('{{.Data.TemplateData.detail_pesanan}}', isWa ? 'Order ID : RS2302270001 \nTanggal dibuat : 30 Januari 2023 \nTanggal reservasi : 15 Februari 2023 \nWaktu reservasi : 09:00 - 15:00 \nStatus Pembayaran : Belum Dibayar \nTotal Tagihan : Rp 880.000' : '<strong>[List Detail Pesanan]</strong>');
    formattedString = formattedString.replace('{{.Data.TemplateData.link_calendar}}', isWa ? '<a href="#" style="color:blue;">https://majoo.id/rsv002938-addtogooglecalendar</a>' : '<strong>[Link Kalender]</strong>');
    formattedString = formattedString.replace('{{.Data.TemplateData.waktu_reservasi}}', isWa ? '\nTanggal reservasi : 15 Februari 2023 \nWaktu reservasi : 09:00 - 15:00' : '<strong>[Informasi tanggal & waktu reservasi]</strong>');
    formattedString = formattedString.replace('{{.Data.TemplateData.info_berulang}}', isWa ? 'Order ID : RS2302270001 \nOrder ID : RS2302270002 \nOrder ID : RS2302270003' : '<strong>[Informasi Reservasi Berulang]]</strong>');
    formattedString = formattedString.replace('{{.Data.TemplateData.waktu_pengingat}}', isWa ? '2 hari' : '<strong>[Waktu Pengingat Reservasi]</strong>');
    return formattedString;
};
