import React, { useEffect, useMemo, useState } from 'react';
import {
    TabsContent,
    Flex,
    Text,
    InputSelect,
    FormGroup,
    FormLabel,
    InputRadioGroup,
    InputRadio,
    Grid,
    Paragraph,
    Box,
    Button,
    DevicePreview,
} from '@majoo-ui/react';
import { ChevronLeftOutline,  ChevronRightOutline} from '@majoo-ui/icons';
import { Controller } from 'react-hook-form';
import { FlexCard, mappingValueContent } from './utils';
import { TAB_VALUE } from './enum';
import CustomBanner from './components/CustomBanner';
import * as whatsAppService from '../../../../data/whatsAppSetting';

const TemplateTab = ({LANG_DATA, watch, control, handleChange }) => {
    const [listTemplate, setListTemplate] = useState([]);
    const [currIndex, setCurrIndex] = useState(0);
    const { templateContent, templateContentWA } = useMemo(() => {
        let content = '';
        if (listTemplate.length) {
            content = listTemplate[currIndex].content;
        }
        return {
            templateContent: mappingValueContent(content),
            templateContentWA: mappingValueContent(content, true),
        };
    } , [listTemplate, currIndex]);
    const typeOptions = [
        { value: 'Notifikasi pembuatan', name: LANG_DATA.FORM.FIELD.TYPE.CREATE},
        { value: 'Notifikasi perubahan', name: LANG_DATA.FORM.FIELD.TYPE.UPDATE},
        { value: 'Notifikasi pengingat', name: LANG_DATA.FORM.FIELD.TYPE.REMINDER},
    ];

    const conditions = [
        { value: 'Tidak berulang', name: LANG_DATA.FORM.FIELD.TYPE.NO_REPEAT},
        { value: 'Notifikasi berulang', name: LANG_DATA.FORM.FIELD.TYPE.REPEAT},
    ];

    const { template: { template_type: templateType, template_condition: templateCondition } } = watch();

    const fetchTemplate = async () => {
        try {
            const payload = {
                feature: 'reservasi',
                template_type: templateType,
                template_condition: templateCondition,
            }
            const res = await whatsAppService.getTemplate(payload);
            setListTemplate(res.data);
            setCurrIndex(0);
        } catch (error) {
            console.error(error);
        }
    }

    useEffect(() => {
        if(templateType && templateCondition) fetchTemplate();
    }, [templateType, templateCondition])

    return (
        <TabsContent value={TAB_VALUE.TEMPLATE}  css={{ boxShadow: 'none' }}>
            <FlexCard
                direction="column"
                wrap="wrap"
                gap={4}
                mt={4}
            >
                <FormGroup responsive="input">
                    <Flex direction="column" gap={2}>
                        <FormLabel css={{ '> span': { color: '$textPrimary', fontSize: '$sectionSubTitle', fontWeight: 600 } }} variant="required">{LANG_DATA.FORM.FIELD.TYPE.TITLE}</FormLabel>
                        <Text color="primary" variant="helper">{LANG_DATA.FORM.FIELD.TYPE.DESCRIPTION}</Text>
                    </Flex>
                    <Flex direction="column" gap={4}>
                        <FormGroup>
                            <FormLabel css={{ '> span': { color: '$textPrimary', fontWeight: 500 } }}>{LANG_DATA.FORM.FIELD.TYPE.TITLE2}</FormLabel>
                            <Controller
                                name="template.template_type"
                                control={control}
                                render={({ field: { value, onChange } }) => (
                                    <InputSelect
                                        value={typeOptions.find(x => x.value === value)}
                                        option={typeOptions}
                                        onChange={(x) => onChange(x.value)}
                                        showSelectAll={false}
                                    />
                                )}
                            />
                        </FormGroup>
                        <FormGroup>
                            <FormLabel css={{ '> span': { color: '$textPrimary', fontWeight: 500 } }}>{LANG_DATA.FORM.FIELD.TYPE.TITLE3}</FormLabel>
                            <Controller
                                name="template.template_condition"
                                control={control}
                                render={({ field: { value, onChange } }) => (
                                    <InputRadioGroup
                                        value={value}
                                        onValueChange={(x) => onChange(x)}
                                        gap={4}
                                        block
                                        outlined
                                    >
                                        {conditions.map(cond => (
                                            <InputRadio id={cond.value} value={cond.value} label={cond.name} />
                                        ))}
                                    </InputRadioGroup>
                                )}
                            />
                        </FormGroup>
                    </Flex>
                </FormGroup>
                <FormGroup responsive="input">
                    <FormLabel>{LANG_DATA.FORM.FIELD.DISPLAY.TITLE}</FormLabel>
                    <Grid columns={2} gap={4}>
                        <Flex direction="column" gap={4}>
                            <CustomBanner>{LANG_DATA.FORM.FIELD.DISPLAY.BANNER}</CustomBanner>
                            <Box css={{ borderRadius: '$lg', background: '$bgGray', padding: '$spacing-05' }}>
                                <Text color="primary">{LANG_DATA.FORM.FIELD.DISPLAY.VARIATION}</Text>
                                <FlexCard css={{ background: '$bgInactive', borderColor: '$gray300' }} mt={2}>
                                    <Paragraph css={{ whiteSpace: 'pre-wrap' }} dangerouslySetInnerHTML={{ __html: templateContent }} />
                                </FlexCard>
                                <Flex align="center" justify="between" mt={2}>
                                    <Button
                                        buttonType="secondary"
                                        onClick={() => setCurrIndex(curr => curr - 1)}
                                        disabled={currIndex === 0}
                                        size="sm"
                                        icon
                                    >
                                        <ChevronLeftOutline color="currentColor" />
                                    </Button>
                                    <Text color="primary">
                                        {currIndex + 1}
                                        {' '}
                                        {LANG_DATA.FORM.FIELD.DISPLAY.OUT_OF}
                                        {' '}
                                        {listTemplate.length}
                                    </Text>
                                    <Button
                                        buttonType="secondary"
                                        onClick={() => setCurrIndex(curr => curr + 1)}
                                        disabled={currIndex >= (listTemplate.length - 1)}
                                        size="sm"
                                        icon
                                    >
                                        <ChevronRightOutline color="currentColor" />
                                    </Button>
                                </Flex>
                            </Box>
                        </Flex>
                        <Flex justify="between" align="center" gap={2}>
                            <Button
                                buttonType="ghost"
                                onClick={() => setCurrIndex(curr => curr - 1)}
                                disabled={currIndex === 0}
                                size="sm"
                                icon
                            >
                                <ChevronLeftOutline color="currentColor" />
                            </Button>
                            <DevicePreview
                                content={{
                                    sender: 'MAJOO',
                                    message: () => (
                                        <span style={{ whiteSpace: 'pre-wrap' }} dangerouslySetInnerHTML={{ __html: templateContentWA }} />
                                    ),
                                }}
                                typeDevice="androidWide"
                                variant="whatsapp"
                                size={200}
                            />
                            <Button
                                buttonType="ghost"
                                onClick={() => setCurrIndex(curr => curr + 1)}
                                disabled={currIndex >= (listTemplate.length - 1)}
                                size="sm"
                                icon
                            >
                                <ChevronRightOutline color="currentColor" />
                            </Button>
                        </Flex>
                    </Grid>
                </FormGroup>
            </FlexCard>
        </TabsContent>
    );
};

export default TemplateTab;
